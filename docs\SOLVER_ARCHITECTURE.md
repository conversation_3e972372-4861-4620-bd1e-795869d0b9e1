# Assignment Service Solver Architecture

## Overview

The Assignment Service solver architecture is built around **Timefold Solver**, providing a flexible, multi-tenant healthcare assignment optimization system. The architecture separates concerns across four main components, enabling dynamic constraint configuration, tenant isolation, and efficient optimization processing.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        API[REST API Endpoints]
        Client[Client Applications]
    end
    
    subgraph "Service Layer"
        SS[Solver Service<br/>solver_service.py]
        SB[Solution Builder<br/>solution_builder.py]
    end
    
    subgraph "Configuration Layer"
        Factory[Solver Factory<br/>factory.py]
        Config[Config Loader<br/>config_loader.py]
        Profiles[YAML Config Profiles]
    end
    
    subgraph "Constraint Layer"
        CP[Constraint Providers]
        C1[Skills Constraints]
        C2[Availability Constraints]
        C3[Task Dependencies]
        CN[... Other Constraints]
    end
    
    subgraph "Timefold Integration"
        TF[Pure Timefold<br/>pure_timefold.py]
        Solver[Timefold Solver Engine]
    end
    
    subgraph "Domain Layer"
        Sol[Assignment Solution<br/>solution.py]
        Models[Domain Models<br/>Provider, Consumer, Task]
    end
    
    Client --> API
    API --> SS
    SS --> SB
    SS --> Factory
    Factory --> Config
    Config --> Profiles
    Factory --> CP
    CP --> C1
    CP --> C2
    CP --> C3
    CP --> CN
    SS --> TF
    TF --> Solver
    SB --> Sol
    SB --> Models
    Sol --> Solver
```

## Component Architecture

### 1. Solver Service (`solver_service.py`)
**Role**: High-level orchestration and tenant management

```mermaid
classDiagram
    class AssignmentSolverService {
        -_solver_managers: Dict[str, SolverManager]
        -_configurations: Dict[str, Dict]
        +solve_assignment(providers, consumers, tasks, tenant_id) AssignmentSolution
        +get_solver_status(tenant_id) Dict
        -_get_solver_manager(tenant_id) SolverManager
        -_create_solver_manager(tenant_id, time_limit)
        -_load_constraint_providers(config) List
    }
    
    class SolverManager {
        +solve(problem_id, solution) SolverJob
    }
    
    AssignmentSolverService --> SolverManager : manages
```

**Key Features**:
- **Tenant Isolation**: Each tenant gets dedicated solver manager
- **Configuration Caching**: Avoids rebuilding identical configurations
- **End-to-End Processing**: Orchestrates complete solving workflow
- **Error Handling**: Comprehensive error management and logging

### 2. Solver Factory (`factory.py`)
**Role**: Dynamic solver construction and constraint discovery

```mermaid
flowchart TD
    Start([Tenant Request]) --> Cache{Check Cache}
    Cache -->|Hit| Return[Return Cached Factory]
    Cache -->|Miss| Load[Load Profile Config]
    Load --> Discover[Auto-Discover Constraints]
    Discover --> Validate[Validate Constraint Set]
    Validate --> Build[Build Constraint Providers]
    Build --> Create[Create Timefold Config]
    Create --> Store[Cache Factory]
    Store --> Return
    
    subgraph "Auto-Discovery Process"
        Discover --> Scan[Scan app.constraints package]
        Scan --> Filter[Filter valid modules]
        Filter --> Register[Register providers]
    end
```

**Key Features**:
- **Auto-Discovery**: Automatically finds constraint providers using `pkgutil.walk_packages`
- **Dynamic Loading**: Imports and configures constraints at runtime
- **Configuration Validation**: Ensures all required constraints are available
- **Caching Strategy**: SHA256 hash-based cache invalidation

### 3. Solution Builder (`solution_builder.py`)
**Role**: Data transformation and solution preparation

```mermaid
sequenceDiagram
    participant Client
    participant SB as Solution Builder
    participant Domain as Domain Models
    participant TS as Time Slots
    participant AS as Assignment Solution
    
    Client->>SB: build_assignment_solution(providers, consumers, tasks)
    SB->>Domain: convert_tasks_to_assignments(tasks)
    Domain-->>SB: List[Assignment]
    SB->>TS: create_time_slots(earliest, latest, interval)
    TS-->>SB: List[datetime]
    SB->>AS: AssignmentSolution(providers, consumers, time_slots, assignments)
    AS-->>SB: Configured solution
    SB->>SB: validate_solution_data(solution)
    SB-->>Client: Ready AssignmentSolution
```

**Key Features**:
- **Data Transformation**: Converts business objects to Timefold entities
- **Time Slot Generation**: Creates planning variable ranges
- **Validation**: Ensures data consistency and completeness
- **Statistics**: Provides solution analysis and metrics

### 4. Pure Timefold Integration (`pure_timefold.py`)
**Role**: Clean Timefold abstraction layer

```python
# Standardized Timefold Configuration
def create_solver_config(constraint_provider_function, time_limit_seconds=30):
    return SolverConfig(
        solution_class=AssignmentSolution,
        entity_class_list=[Assignment],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=constraint_provider_function
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=time_limit_seconds)
        )
    )
```

## Domain Model Architecture

### Assignment Solution Structure

```mermaid
classDiagram
    class AssignmentSolution {
        +providers: List[Provider] @ProblemFactCollectionProperty
        +consumers: List[Consumer] @ProblemFactCollectionProperty
        +time_slots: List[datetime] @ValueRangeProvider
        +assignments: List[Assignment] @PlanningEntityCollectionProperty
        +score: HardSoftScore @PlanningScore
        +tenant_id: str
        +optimization_config: dict
        
        +total_assignments: int
        +assigned_count: int
        +assignment_rate: float
        +validate_solution() List[str]
        +get_solution_summary() dict
    }
    
    class Assignment {
        +id: str @PlanningId
        +consumer_id: str
        +task_type: str
        +required_skills: List[str]
        +duration_min: int
        +earliest_start: datetime
        +latest_end: datetime
        +sequence_group: str
        +sequence_order: int
        +prerequisite_task_ids: List[str]
        
        +assigned_provider: Provider @PlanningVariable
        +assigned_start_time: datetime @PlanningVariable
        
        +is_assigned: bool
        +assigned_end_time: datetime
    }
    
    class Provider {
        +id: str
        +name: str
        +skills: List[str]
        +availability: str
        +location: Location
    }
    
    AssignmentSolution "1" --> "*" Assignment : optimizes
    AssignmentSolution "1" --> "*" Provider : uses
    Assignment "*" --> "0..1" Provider : assigned_to
```

## Constraint System Architecture

### Constraint Discovery and Loading

```mermaid
flowchart LR
    subgraph "Auto-Discovery"
        CP[Constraints Package] --> Scan[pkgutil.walk_packages]
        Scan --> Registry[Provider Registry]
    end
    
    subgraph "Configuration Loading"
        YAML[tenant.yml] --> Config[Config Loader]
        Config --> ConstraintSet[Constraint Set]
        ConstraintSet --> Weights[Constraint Weights]
        Config --> Parameters[Constraint Parameters]
    end
    
    subgraph "Dynamic Loading"
        Registry --> Import[importlib.import_module]
        ConstraintSet --> Import
        Import --> Function[Get Constraint Function]
        Function --> Provider[Constraint Provider]
        Weights --> Provider
        Parameters --> Provider
    end
    
    Provider --> Timefold[Timefold Constraint Factory]
```

### Constraint Provider Pattern

```python
# Standard Constraint Provider Pattern
def skills_constraints(parameters: Dict[str, Any]):
    """Create skills constraint provider with parameters."""
    
    def create_constraints(constraint_factory):
        """Create the actual constraints."""
        return [
            constraint_factory
                .for_each(Assignment)
                .filter(lambda assignment: assignment.assigned_provider is not None)
                .filter(lambda assignment: not has_required_skills(assignment))
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint("Missing required skills")
        ]
    
    return create_constraints
```

## Data Flow Architecture

### Complete Optimization Workflow

```mermaid
sequenceDiagram
    participant C as Client
    participant SS as Solver Service
    participant F as Factory
    participant CL as Config Loader
    participant SB as Solution Builder
    participant T as Timefold Solver
    
    C->>SS: solve_assignment(providers, consumers, tasks, tenant_id)
    SS->>F: build_factory(tenant_id)
    F->>CL: load_profile(tenant_id)
    CL-->>F: Configuration (constraints, weights, parameters)
    F->>F: Auto-discover constraint providers
    F->>F: Load and configure constraints
    F->>T: Create SolverFactory with constraints
    F-->>SS: Configured SolverFactory
    
    SS->>SB: build_assignment_solution(providers, consumers, tasks)
    SB->>SB: convert_tasks_to_assignments()
    SB->>SB: create_time_slots()
    SB-->>SS: Initial AssignmentSolution
    
    SS->>T: solve(problem_id, solution)
    T->>T: Apply constraints and optimize
    T-->>SS: Optimized AssignmentSolution
    SS-->>C: Final solution with assignments
```

## Multi-Tenant Architecture

### Tenant Isolation Strategy

```mermaid
graph TB
    subgraph "Tenant A"
        ConfigA[Config Profile A]
        ConstraintsA[Constraints: Skills, Availability]
        SolverA[Solver Manager A]
        CacheA[Solution Cache A]
    end
    
    subgraph "Tenant B"
        ConfigB[Config Profile B]
        ConstraintsB[Constraints: Skills, Travel, Dependencies]
        SolverB[Solver Manager B]
        CacheB[Solution Cache B]
    end
    
    subgraph "Shared Infrastructure"
        Factory[Solver Factory]
        Constraints[Constraint Provider Registry]
        Timefold[Timefold Engine]
    end
    
    ConfigA --> Factory
    ConfigB --> Factory
    Factory --> Constraints
    Factory --> SolverA
    Factory --> SolverB
    SolverA --> Timefold
    SolverB --> Timefold
    SolverA --> CacheA
    SolverB --> CacheB
```

**Isolation Features**:
- **Configuration Isolation**: Each tenant has separate YAML configuration
- **Constraint Isolation**: Different constraint sets per tenant
- **Solver Isolation**: Dedicated solver managers per tenant
- **Cache Isolation**: Separate caching per tenant

## Performance Architecture

### Caching Strategy

```mermaid
flowchart TD
    Request[Solver Request] --> CheckFactory{Factory Cached?}
    CheckFactory -->|Yes| CheckConfig{Config Changed?}
    CheckConfig -->|No| UseCache[Use Cached Factory]
    CheckConfig -->|Yes| Rebuild[Rebuild Factory]
    CheckFactory -->|No| Rebuild
    
    Rebuild --> LoadConfig[Load Configuration]
    LoadConfig --> HashConfig[Generate Config Hash]
    HashConfig --> BuildFactory[Build New Factory]
    BuildFactory --> CacheFactory[Cache Factory]
    CacheFactory --> UseFactory[Use Factory]
    UseCache --> UseFactory
    
    subgraph "Cache Management"
        ConfigHash[SHA256 Config Hash]
        Timestamp[Creation Timestamp]
        InvalidationKey[Cache Key: tenant_id]
    end
```

**Caching Benefits**:
- **Configuration Hashing**: SHA256-based cache invalidation
- **Factory Reuse**: Avoid rebuilding identical solver configurations
- **Memory Efficiency**: Per-tenant cache management
- **Hot Reload Support**: Automatic cache invalidation on config changes

## Error Handling Architecture

### Comprehensive Error Management

```mermaid
flowchart TD
    Request[Solver Request] --> Validate{Validate Input}
    Validate -->|Invalid| InputError[Input Validation Error]
    Validate -->|Valid| LoadConfig{Load Configuration}
    LoadConfig -->|Failed| ConfigError[Configuration Error]
    LoadConfig -->|Success| LoadConstraints{Load Constraints}
    LoadConstraints -->|Failed| ConstraintError[Constraint Loading Error]
    LoadConstraints -->|Success| BuildSolution{Build Solution}
    BuildSolution -->|Failed| SolutionError[Solution Building Error]
    BuildSolution -->|Success| Solve{Solve Problem}
    Solve -->|Failed| SolverError[Solver Execution Error]
    Solve -->|Success| Success[Return Solution]
    
    InputError --> Log[Log Error]
    ConfigError --> Log
    ConstraintError --> Log
    SolutionError --> Log
    SolverError --> Log
    Log --> Return[Return Error Response]
```

## Configuration Architecture

### YAML-Based Configuration System

```yaml
# Example tenant configuration
profile: healthcare_tenant_001
constraint_set:
  - c001_skills
  - c002_provider_avail
  - c003_overlap
  - c013_task_sequence
  - c014_task_dependencies

weights:
  c001_skills: hard:2000000
  c002_provider_avail: hard:2000000
  c013_task_sequence: hard:2000000
  c014_task_dependencies: hard:1500000

parameters:
  c001_skills:
    required_certifications: [RN, LPN, CNA]
    specialty_skills: [wound_care, medication_management]
  c013_task_sequence:
    sequence_enforcement: strict
    same_provider_sequences: true
    sequence_buffer_minutes: 15
```

## Extensibility Architecture

### Adding New Constraints

1. **Create Constraint Module**: `app/constraints/new_constraint.py`
2. **Implement Standard Pattern**:
   ```python
   def new_constraint_constraints(parameters: Dict[str, Any]):
       def create_constraints(constraint_factory):
           # Constraint implementation
           return [constraint_factory.for_each(Assignment)...] 
       return create_constraints
   ```
3. **Auto-Discovery**: Constraint automatically discovered by factory
4. **Configuration**: Add to tenant YAML configuration

### Adding New Planning Variables

1. **Extend Assignment Entity**: Add new `@PlanningVariable` fields
2. **Extend Value Range Providers**: Add new `@ValueRangeProvider` in solution
3. **Update Constraints**: Modify constraints to consider new variables
4. **Update Solution Builder**: Handle new variable initialization

## Monitoring and Observability

### Key Metrics and Logging

```mermaid
graph LR
    subgraph "Performance Metrics"
        ST[Solving Time]
        CF[Cache Hit Rate]
        CR[Constraint Loading Rate]
        SR[Solution Quality Score]
    end
    
    subgraph "Operational Metrics"
        TE[Tenant Activity]
        ER[Error Rates]
        RM[Resource Usage]
        TH[Throughput]
    end
    
    subgraph "Business Metrics"
        AR[Assignment Rate]
        PS[Provider Utilization]
        CS[Consumer Satisfaction]
        OE[Optimization Effectiveness]
    end
    
    subgraph "Logging Levels"
        DEBUG[DEBUG: Detailed flow]
        INFO[INFO: Key operations]
        WARNING[WARNING: Recoverable issues]
        ERROR[ERROR: Critical failures]
    end
```

## Deployment Architecture

### Production Considerations

- **Horizontal Scaling**: Multiple solver service instances
- **Load Balancing**: Distribute tenant requests
- **Configuration Management**: Hot reload capabilities
- **Resource Limits**: Memory and CPU constraints per tenant
- **Health Checks**: Solver service availability monitoring
- **Metrics Collection**: Performance and business metrics

This architecture provides a robust, scalable, and maintainable foundation for healthcare assignment optimization with strong separation of concerns, tenant isolation, and comprehensive error handling.
