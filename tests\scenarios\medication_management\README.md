# Medication Management Scenario

## Overview
This scenario tests the assignment service's ability to handle complex medication management workflows that require sequential task execution and provider coordination.

## Healthcare Workflow
**Medication Management Episode**: A patient requires comprehensive medication management involving multiple healthcare providers in a specific sequence.

### Task Sequence
1. **Pharmacist Consultation** (30 min)
   - Medication review and reconciliation
   - Drug interaction screening
   - Dosage optimization
   - Required skills: `medication_review`, `drug_interactions`

2. **Nurse Administration** (20 min)
   - Medication administration
   - Patient education on new medications
   - Side effect monitoring setup
   - Required skills: `medication_admin`, `patient_education`
   - **Dependency**: Must occur after pharmacist consultation
   - **Same Provider**: Not required (different specialties)

3. **Documentation** (15 min)
   - Update medication list in EHR
   - Document patient response
   - Schedule follow-up monitoring
   - Required skills: `documentation`, `ehr_systems`
   - **Dependency**: Must occur after nurse administration
   - **Same Provider**: Can be any qualified provider

## Test Objectives
- **Sequential Dependencies**: Ensure proper ordering of medication management tasks
- **Cross-Provider Coordination**: Test handoffs between different healthcare specialties
- **Time Window Constraints**: Respect patient availability and provider schedules
- **Skill Matching**: Assign tasks to providers with appropriate qualifications
- **Documentation Requirements**: Ensure all activities are properly documented

## Expected Outcomes
- All tasks assigned to qualified providers
- Proper sequence maintained (Pharmacist → Nurse → Documentation)
- No scheduling conflicts
- Optimal use of provider time
- Complete medication management workflow

## Configuration
- **Profile**: `medication_management_constraints`
- **Constraint Focus**: Task dependencies, skill matching, time optimization
- **Priority**: Patient safety through proper medication management sequence
