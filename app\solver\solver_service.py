"""
Timefold Solver Service
======================

Complete solver service implementation following Timefold best practices.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

from timefold.solver import SolverFactory, SolverManager
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration

from app.domain.solution import AssignmentSolution, Assignment
from app.domain.models import Provider, Consumer, Task
from app.solver.solution_builder import build_assignment_solution
from app.core.config_loader import load_profile
from app.solver.factory import get_available_constraints, _load_constraint_provider

logger = logging.getLogger(__name__)


class AssignmentSolverService:
    """
    Complete solver service for healthcare assignment optimization.
    
    This service provides a high-level interface for solving assignment problems
    using Timefold solver with proper configuration and constraint management.
    """
    
    def __init__(self):
        self._solver_managers: Dict[str, SolverManager] = {}
        self._configurations: Dict[str, Dict[str, Any]] = {}
    
    async def solve_assignment(
        self,
        providers: List[Provider],
        consumers: List[Consumer],
        tasks: List[Task],
        tenant_id: str = "default",
        time_limit_seconds: int = 30
    ) -> AssignmentSolution:
        """
        Solve assignment problem using Timefold solver.
        
        Args:
            providers: Available healthcare providers
            consumers: Patients/consumers needing care
            tasks: Tasks to be assigned
            tenant_id: Tenant configuration to use
            time_limit_seconds: Maximum solving time
            
        Returns:
            Optimized assignment solution
        """
        try:
            # Get or create solver manager for tenant
            solver_manager = await self._get_solver_manager(tenant_id, time_limit_seconds)
            
            # Build initial solution
            solution = build_assignment_solution(
                providers=providers,
                consumers=consumers,
                tasks=tasks,
                tenant_id=tenant_id,
                config=self._configurations.get(tenant_id, {})
            )
            
            logger.info(f"Starting optimization for {len(tasks)} tasks with {len(providers)} providers")
            
            # Solve the problem
            problem_id = f"{tenant_id}_{datetime.now().isoformat()}"
            solver_job = solver_manager.solve(problem_id, solution)
            
            # Get the result
            final_solution = solver_job.get_final_best_solution()
            
            logger.info(f"Optimization completed. Score: {final_solution.score}")
            
            return final_solution
            
        except Exception as e:
            logger.error(f"Failed to solve assignment problem: {e}")
            raise
    
    async def _get_solver_manager(self, tenant_id: str, time_limit_seconds: int) -> SolverManager:
        """Get or create solver manager for tenant."""
        if tenant_id not in self._solver_managers:
            await self._create_solver_manager(tenant_id, time_limit_seconds)
        return self._solver_managers[tenant_id]
    
    async def _create_solver_manager(self, tenant_id: str, time_limit_seconds: int):
        """Create solver manager for tenant with proper configuration."""
        try:
            # Load configuration
            config = await load_profile(tenant_id)
            self._configurations[tenant_id] = config
            
            # Load constraint providers
            constraint_providers = await self._load_constraint_providers(config)
            
            # Create constraint provider function
            def define_constraints(constraint_factory):
                """Combine all constraint providers."""
                constraints = []
                parameters = config.get('parameters', {})
                
                for provider_func in constraint_providers:
                    try:
                        # Get constraint creator function
                        constraint_creator = provider_func(parameters)
                        # Create constraint
                        constraint = constraint_creator(constraint_factory)
                        constraints.append(constraint)
                    except Exception as e:
                        logger.warning(f"Failed to create constraint from {provider_func}: {e}")
                
                return constraints
            
            # Create solver configuration
            solver_config = SolverConfig(
                solution_class=AssignmentSolution,
                entity_class_list=[Assignment],
                score_director_factory_config=ScoreDirectorFactoryConfig(
                    constraint_provider_function=define_constraints
                ),
                termination_config=TerminationConfig(
                    spent_limit=Duration(seconds=time_limit_seconds)
                )
            )
            
            # Create solver factory and manager
            solver_factory = SolverFactory.create(solver_config)
            solver_manager = SolverManager.create(solver_factory)
            
            self._solver_managers[tenant_id] = solver_manager
            
            logger.info(f"Created solver manager for tenant {tenant_id}")
            
        except Exception as e:
            logger.error(f"Failed to create solver manager for tenant {tenant_id}: {e}")
            raise
    
    async def _load_constraint_providers(self, config: Dict[str, Any]) -> List:
        """Load constraint providers based on configuration."""
        constraint_providers = []
        constraint_set = config.get('constraint_set', [])
        
        for constraint_name in constraint_set:
            try:
                provider_func = _load_constraint_provider(constraint_name, config.get('parameters', {}))
                constraint_providers.append(provider_func)
            except Exception as e:
                logger.warning(f"Failed to load constraint provider {constraint_name}: {e}")
        
        return constraint_providers
    
    def get_solver_status(self, tenant_id: str) -> Dict[str, Any]:
        """Get status information for tenant solver."""
        if tenant_id not in self._solver_managers:
            return {"status": "not_initialized"}
        
        return {
            "status": "ready",
            "tenant_id": tenant_id,
            "configuration_loaded": tenant_id in self._configurations,
            "constraint_count": len(self._configurations.get(tenant_id, {}).get('constraint_set', []))
        }


# Global solver service instance
_solver_service = None


def get_solver_service() -> AssignmentSolverService:
    """Get the global solver service instance."""
    global _solver_service
    if _solver_service is None:
        _solver_service = AssignmentSolverService()
    return _solver_service


async def solve_healthcare_assignment(
    providers: List[Provider],
    consumers: List[Consumer],
    tasks: List[Task],
    tenant_id: str = "default",
    time_limit_seconds: int = 30
) -> AssignmentSolution:
    """
    Convenience function for solving healthcare assignment problems.
    
    Args:
        providers: Available healthcare providers
        consumers: Patients/consumers needing care
        tasks: Tasks to be assigned
        tenant_id: Tenant configuration to use
        time_limit_seconds: Maximum solving time
        
    Returns:
        Optimized assignment solution
    """
    solver_service = get_solver_service()
    return await solver_service.solve_assignment(
        providers=providers,
        consumers=consumers,
        tasks=tasks,
        tenant_id=tenant_id,
        time_limit_seconds=time_limit_seconds
    )


def create_demo_solution() -> AssignmentSolution:
    """Create a demo solution for testing purposes."""
    from app.solver.solution_builder import create_test_solution
    return create_test_solution("healthcare_demo")
