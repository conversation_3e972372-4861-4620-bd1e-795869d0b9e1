
"""
Main FastAPI application for the Assignment Service.

This module sets up the FastAPI application with all routers, middleware,
and lifecycle management.
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON>AP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.api.solver_router import router as solver_router
from app.api.health_router import router as health_router
from app.core.db import init_database
from app.core.config_loader import start_config_watcher, stop_config_watcher, reload_watcher
from app.core.rls import RLSMiddleware
from app.core.metrics import metrics_update_loop

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.

    Handles startup and shutdown tasks including:
    - Database initialization
    - Configuration file watching
    - Background tasks
    """
    logger.info("Starting Assignment Service...")

    # Startup tasks
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized successfully")

        # Start configuration file watcher
        await start_config_watcher()
        logger.info("Configuration file watcher started")

        # Start background reload watcher
        reload_task = asyncio.create_task(reload_watcher())
        logger.info("Configuration reload watcher started")

        # Start metrics update loop
        metrics_task = asyncio.create_task(metrics_update_loop())
        logger.info("Metrics update loop started")

        logger.info("Assignment Service startup completed")

    except Exception as e:
        logger.error(f"Failed to start Assignment Service: {e}")
        raise

    yield

    # Shutdown tasks
    logger.info("Shutting down Assignment Service...")

    try:
        # Stop configuration file watcher
        await stop_config_watcher()
        logger.info("Configuration file watcher stopped")

        # Cancel background tasks
        reload_task.cancel()
        metrics_task.cancel()
        try:
            await reload_task
            await metrics_task
        except asyncio.CancelledError:
            pass
        logger.info("Background tasks stopped")

        logger.info("Assignment Service shutdown completed")

    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="Assignment Service",
    description="Multi-tenant assignment optimization service using Timefold-Py",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add RLS middleware for tenant isolation
app.add_middleware(RLSMiddleware)

# Include routers
app.include_router(solver_router)
app.include_router(health_router)


@app.get("/", tags=["root"])
async def root():
    """Root endpoint providing service information."""
    return {
        "service": "Assignment Service",
        "version": "1.0.0",
        "description": "Multi-tenant assignment optimization service",
        "docs": "/docs",
        "health": "/health"
    }


# Health endpoints are now handled by health_router


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler with structured error responses."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "type": "http_error"
            },
            "request_id": getattr(request.state, 'request_id', None),
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": 500,
                "message": "Internal server error",
                "type": "internal_error"
            },
            "request_id": getattr(request.state, 'request_id', None),
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )


@app.middleware("http")
async def add_request_id_middleware(request: Request, call_next):
    """Add request ID to all requests for tracing."""
    import uuid
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id

    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id

    return response


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Log all requests and responses."""
    import time

    start_time = time.time()

    # Log request
    logger.info(
        f"Request: {request.method} {request.url.path} "
        f"from {request.client.host if request.client else 'unknown'}"
    )

    response = await call_next(request)

    # Log response
    process_time = time.time() - start_time
    logger.info(
        f"Response: {response.status_code} "
        f"in {process_time:.3f}s"
    )

    return response


# Development server configuration
if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
