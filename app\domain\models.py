
from uuid import UUID
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel


class Location(BaseModel):
    """Geographic location with coordinates and optional address."""
    lat: float
    lng: float
    address: Optional[str] = None


class Provider(BaseModel):
    """Healthcare provider or service provider."""
    id: UUID
    name: str
    skills: List[str] = []
    capacity: int = 1

    # ALL properties promoted to first-class fields for maximum type safety
    location: Optional[Location] = None
    experience_years: Optional[int] = None
    languages: List[str] = []
    transportation: Optional[str] = None
    shift_start: Optional[str] = None
    shift_end: Optional[str] = None
    availability: Dict[str, Any] = {}
    current_task_count: int = 0
    critical: bool = False

    # Healthcare role specification
    role: Optional[str] = None                          # "Registered Nurse", "CNA", "LPN"

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = {}


class Consumer(BaseModel):
    """Patient or service consumer."""
    id: UUID
    name: str

    # ALL properties promoted to first-class fields for maximum type safety
    location: Optional[Location] = None
    age: Optional[int] = None
    preferred_language: Optional[str] = None
    conditions: List[str] = []
    mobility: Optional[str] = None
    care_episode_id: Optional[str] = None
    insurance: Optional[str] = None

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = {}


class Task(BaseModel):
    """Work assignment to be optimized."""
    id: UUID
    consumer_id: UUID
    required_skills: List[str]
    duration_min: int
    earliest_start: Optional[datetime] = None
    latest_end: Optional[datetime] = None
    urgent: bool = False

    # ALL properties promoted to first-class fields for maximum type safety
    assigned_provider: Optional[UUID] = None  # Planning variable
    location: Optional[Location] = None
    care_episode_id: Optional[str] = None
    task_type: Optional[str] = None

    # Scheduling and timing fields
    assigned_start_time: Optional[datetime] = None
    assigned_end_time: Optional[datetime] = None
    travel_time_to_next: Optional[int] = None

    # Task metadata fields
    required: bool = True
    priority: str = "normal"  # critical, high, normal, low
    visit_group: Optional[str] = None
    required_visit_parts: List[str] = []
    completed_visit_parts: List[str] = []
    visit_sequence_critical: bool = False
    strict_timing: bool = False
    appointment_type: Optional[str] = None

    # Patient and care context (from test data)
    patient_name: Optional[str] = None
    patient_age: Optional[int] = None
    conditions: List[str] = []
    mobility: Optional[str] = None
    insurance: Optional[str] = None
    visit_type: Optional[str] = None

    # Task Dependencies - for sequential care episodes
    sequence_group: Optional[str] = None                    # "care_episode_123" - groups related tasks
    sequence_order: Optional[int] = None                    # 1, 2, 3, 4 - order within sequence
    prerequisite_task_ids: List[str] = []                   # ["task_1", "task_2"] - must complete first
    same_provider_required: bool = False                    # True if must use same provider as prerequisites
    dependency_type: str = "none"                          # "none", "sequence", "completion", "resource"

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = {}
