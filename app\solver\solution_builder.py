"""
Solution Builder for Assignment Optimization
==========================================

Converts domain objects (providers, consumers, tasks) into Timefold planning solution.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta

from app.domain.models import Provider, Consumer, Task
from app.domain.solution import AssignmentSolution, Assignment, create_time_slots, convert_tasks_to_assignments


def build_assignment_solution(
    providers: List[Provider],
    consumers: List[Consumer], 
    tasks: List[Task],
    tenant_id: str = "",
    config: Dict[str, Any] = None
) -> AssignmentSolution:
    """
    Build a complete AssignmentSolution from domain objects.
    
    Args:
        providers: List of available healthcare providers
        consumers: List of patients/consumers needing care
        tasks: List of tasks to be assigned
        tenant_id: Tenant identifier
        config: Optimization configuration
        
    Returns:
        Complete AssignmentSolution ready for optimization
    """
    if config is None:
        config = {}
    
    # Convert tasks to assignment planning entities
    assignments = convert_tasks_to_assignments(tasks, consumers)
    
    # Create time slots for planning variables
    time_slots = _create_planning_time_slots(tasks, config)
    
    # Build the solution
    solution = AssignmentSolution(
        providers=providers,
        consumers=consumers,
        time_slots=time_slots,
        assignments=assignments,
        tenant_id=tenant_id,
        optimization_config=config
    )
    
    return solution


def _create_planning_time_slots(tasks: List[Task], config: Dict[str, Any]) -> List[datetime]:
    """
    Create time slots for planning based on task time windows.
    
    Args:
        tasks: List of tasks to analyze for time windows
        config: Configuration with time slot settings
        
    Returns:
        List of datetime objects representing available time slots
    """
    if not tasks:
        # Default time slots if no tasks provided
        from datetime import datetime, time
        start_time = datetime.combine(datetime.today(), time(8, 0))  # 8 AM
        end_time = datetime.combine(datetime.today(), time(18, 0))   # 6 PM
        return create_time_slots(start_time, end_time, 15)
    
    # Find the overall time window from all tasks
    earliest_start = min(task.earliest_start for task in tasks)
    latest_end = max(task.latest_end for task in tasks)
    
    # Get time slot interval from config
    interval_minutes = config.get('time_slot_interval_minutes', 15)
    
    # Create time slots
    return create_time_slots(earliest_start, latest_end, interval_minutes)


def create_test_solution(scenario_name: str = "basic") -> AssignmentSolution:
    """
    Create a test solution for demonstration and testing.
    
    Args:
        scenario_name: Name of the test scenario to create
        
    Returns:
        Test AssignmentSolution with sample data
    """
    from datetime import datetime, time
    from app.domain.models import Location
    
    # Create test providers
    providers = [
        Provider(
            id="provider_001",
            name="Sarah Johnson, RN",
            skills=["medication_admin", "wound_care", "patient_education"],
            location=Location(latitude=40.7128, longitude=-74.0060, address="New York, NY"),
            availability_windows=[],
            properties={}
        ),
        Provider(
            id="provider_002", 
            name="Michael Chen, PT",
            skills=["physical_therapy", "mobility_assessment", "exercise_planning"],
            location=Location(latitude=40.7589, longitude=-73.9851, address="New York, NY"),
            availability_windows=[],
            properties={}
        )
    ]
    
    # Create test consumers
    consumers = [
        Consumer(
            id="consumer_001",
            name="Mary Smith",
            location=Location(latitude=40.7505, longitude=-73.9934, address="New York, NY"),
            care_level="skilled_nursing",
            properties={}
        )
    ]
    
    # Create test time slots
    today = datetime.today()
    start_time = datetime.combine(today, time(9, 0))   # 9 AM
    end_time = datetime.combine(today, time(17, 0))    # 5 PM
    time_slots = create_time_slots(start_time, end_time, 30)  # 30-minute intervals
    
    # Create test assignments
    assignments = [
        Assignment(
            id="assignment_001",
            consumer_id="consumer_001",
            task_type="medication_administration",
            required_skills=["medication_admin"],
            duration_min=30,
            earliest_start=start_time,
            latest_end=end_time,
            priority="high",
            location=consumers[0].location,
            properties={}
        ),
        Assignment(
            id="assignment_002",
            consumer_id="consumer_001", 
            task_type="wound_care_assessment",
            required_skills=["wound_care"],
            duration_min=45,
            earliest_start=start_time + timedelta(hours=1),
            latest_end=end_time,
            priority="medium",
            location=consumers[0].location,
            properties={}
        )
    ]
    
    # Build solution
    solution = AssignmentSolution(
        providers=providers,
        consumers=consumers,
        time_slots=time_slots,
        assignments=assignments,
        tenant_id="test_tenant",
        optimization_config={"scenario": scenario_name}
    )
    
    return solution


def validate_solution_data(solution: AssignmentSolution) -> List[str]:
    """
    Validate solution data for completeness and consistency.
    
    Args:
        solution: AssignmentSolution to validate
        
    Returns:
        List of validation error messages (empty if valid)
    """
    errors = []
    
    # Check required data
    if not solution.providers:
        errors.append("No providers available for assignment")
    
    if not solution.assignments:
        errors.append("No assignments to optimize")
        
    if not solution.time_slots:
        errors.append("No time slots available for scheduling")
    
    # Check assignment references
    consumer_ids = {consumer.id for consumer in solution.consumers}
    for assignment in solution.assignments:
        if assignment.consumer_id not in consumer_ids:
            errors.append(f"Assignment {assignment.id} references unknown consumer {assignment.consumer_id}")
    
    # Check skill availability
    available_skills = set()
    for provider in solution.providers:
        available_skills.update(provider.skills)
    
    for assignment in solution.assignments:
        missing_skills = set(assignment.required_skills) - available_skills
        if missing_skills:
            errors.append(f"Assignment {assignment.id} requires unavailable skills: {missing_skills}")
    
    # Check time window consistency
    for assignment in solution.assignments:
        if assignment.earliest_start >= assignment.latest_end:
            errors.append(f"Assignment {assignment.id} has invalid time window")
    
    return errors


def get_solution_statistics(solution: AssignmentSolution) -> Dict[str, Any]:
    """
    Get comprehensive statistics about the solution.
    
    Args:
        solution: AssignmentSolution to analyze
        
    Returns:
        Dictionary with solution statistics
    """
    stats = {
        "providers": len(solution.providers),
        "consumers": len(solution.consumers), 
        "assignments": len(solution.assignments),
        "time_slots": len(solution.time_slots),
        "assigned_count": solution.assigned_count,
        "unassigned_count": solution.unassigned_count,
        "assignment_rate": f"{solution.assignment_rate:.1f}%",
        "validation_errors": len(validate_solution_data(solution))
    }
    
    # Skill analysis
    all_skills = set()
    for provider in solution.providers:
        all_skills.update(provider.skills)
    
    required_skills = set()
    for assignment in solution.assignments:
        required_skills.update(assignment.required_skills)
    
    stats["available_skills"] = len(all_skills)
    stats["required_skills"] = len(required_skills)
    stats["skill_coverage"] = len(required_skills & all_skills) / len(required_skills) if required_skills else 1.0
    
    # Time window analysis
    if solution.assignments:
        earliest = min(assignment.earliest_start for assignment in solution.assignments)
        latest = max(assignment.latest_end for assignment in solution.assignments)
        stats["planning_window_hours"] = (latest - earliest).total_seconds() / 3600
    
    return stats
