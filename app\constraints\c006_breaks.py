# app/constraints/c006_breaks.py
"""
c006_breaks: Break time constraint.
Priority: 6 - Ensures resources get adequate breaks between tasks.
"""

from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c006_breaks_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "min_break_minutes": int  # Minimum break time between tasks
        }
    """
    min_break = cfg.get("min_break_minutes", 30)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Task, lambda t1, t2: (
                       t1.id != t2.id and
                       t1.assigned_provider == t2.assigned_provider and
                       t1.assigned_provider is not None and
                       _insufficient_break_time(t1, t2, min_break)
                   ))
                   .penalize("INSUFFICIENT_BREAK_TIME", HardSoftScore.ONE_HARD)
        ]
    return define_constraints

def _insufficient_break_time(task1: Task, task2: Task, min_break_minutes: int) -> bool:
    """Check if there's insufficient break time between tasks."""
    # Simple check using earliest_start and latest_end
    if not all([task1.earliest_start, task1.latest_end, task2.earliest_start, task2.latest_end]):
        return False
    
    # Check if task2 starts too soon after task1 ends
    if task1.latest_end and task2.earliest_start:
        from datetime import timedelta
        min_gap = timedelta(minutes=min_break_minutes)
        return (task2.earliest_start - task1.latest_end) < min_gap
    
    return False
