# app/constraints/c003_task_overlap.py
"""
c003_task_overlap: Task overlap constraint.
Priority: 3 - Prevents tasks assigned to the same resource from overlapping in time.
This is a fundamental hard constraint for scheduling.
"""

from timefold.solver.score import constraint_provider, HardSoftScore
from app.domain.models import Task

def c003_task_overlap_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "minimum_gap_minutes": int  # Minimum gap between tasks
        }
    """
    min_gap = cfg.get("minimum_gap_minutes", 0)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Task, lambda t1, t2: (
                       t1.id != t2.id and
                       t1.assigned_provider == t2.assigned_provider and
                       t1.assigned_provider is not None and
                       _tasks_overlap(t1, t2, min_gap)
                   ))
                   .penalize("TASK_TIME_OVERLAP_PREVENTION", HardSoftScore.ONE_HARD)
        ]
    return define_constraints

def _tasks_overlap(task1: Task, task2: Task, min_gap_minutes: int) -> bool:
    """Check if two tasks overlap considering minimum gap."""
    # Simple overlap check using earliest_start and latest_end
    if not all([task1.earliest_start, task1.latest_end, task2.earliest_start, task2.latest_end]):
        return False
    
    from datetime import timedelta
    gap = timedelta(minutes=min_gap_minutes)
    
    # Type assertion since we checked above
    assert task1.earliest_start is not None
    assert task1.latest_end is not None
    assert task2.earliest_start is not None
    assert task2.latest_end is not None
    
    return (task1.earliest_start < task2.latest_end + gap) and (task2.earliest_start < task1.latest_end + gap)
