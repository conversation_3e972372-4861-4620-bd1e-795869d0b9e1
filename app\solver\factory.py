
"""
Solver factory for building tenant-specific Timefold solvers.

This module dynamically assembles constraint providers based on tenant configuration
and applies constraint weights for runtime tuning.
"""

import importlib
import yaml
import os
import time
import pkgutil
import logging
import asyncio
import json
import hashlib

from pathlib import Path
from typing import Dict, Any, Tuple, List, Optional
# Direct Timefold imports following standard pattern
from timefold.solver import SolverFactory, SolverManager
from timefold.solver.config import (
    SolverConfig,
    ScoreDirectorFactoryConfig,
    TerminationConfig,
    Duration
)

from app.domain.solution import AssignmentSolution, Assignment
from fastapi import HTTPException
from app.core.config_loader import load_profile

logger = logging.getLogger(__name__)

# Auto-discover constraint providers at module load time
# Using your suggested approach with pkgutil.walk_packages
import app.constraints
constraints_path = [os.path.dirname(app.constraints.__file__)]
_PROVIDERS = {
    m.name.split('.')[-1]: m.name
    for m in pkgutil.walk_packages(constraints_path, 'app.constraints.')
    if not m.ispkg and not m.name.split('.')[-1].startswith('_')
}

# Validate discovery worked, fallback to static registry if needed
if not _PROVIDERS:
    logger.warning("Auto-discovery found no providers, using static registry")
    _PROVIDERS = {
        "c001_skill_match": "app.constraints.c001_skill_match",
        "c002_provider_availability": "app.constraints.c002_provider_availability",
        "c003_task_overlap": "app.constraints.c003_task_overlap",
        "c004_task_completion": "app.constraints.c004_task_completion",
        "c005_travel_distance": "app.constraints.c005_travel_distance",
        "c006_break_time": "app.constraints.c006_break_time",
        "c007_urgent_priority": "app.constraints.c007_urgent_priority",
        "c008_care_continuity": "app.constraints.c008_care_continuity",
        "c009_patient_preference": "app.constraints.c009_patient_preference",
        "c010_provider_preference": "app.constraints.c010_provider_preference",
        "c011_travel_optimization": "app.constraints.c011_travel_optimization",
        "c012_workload_optimization": "app.constraints.c012_workload_optimization",
        "c013_sequence_order": "app.constraints.c013_sequence_order",
        "c014_task_dependencies": "app.constraints.c014_task_dependencies"
    }
else:
    logger.info(f"Auto-discovered {len(_PROVIDERS)} constraint providers: {list(_PROVIDERS.keys())}")

# Global cache for solver factories
_solver_cache: Dict[str, Dict[str, Any]] = {}
_cache_lock = asyncio.Lock()


def get_provider_registry() -> Dict[str, str]:
    """Get the current provider registry."""
    logger.info(f"Available constraint providers: {list(_PROVIDERS.keys())}")
    return _PROVIDERS


async def build_factory(tenant_id: str, force_rebuild: bool = False) -> SolverFactory:
    """
    Build a tenant-specific solver factory with caching.

    Args:
        tenant_id: The tenant identifier
        force_rebuild: Force rebuild even if cached version exists

    Returns:
        Configured SolverFactory instance

    Raises:
        FileNotFoundError: If configuration profile is not found
        ValueError: If configuration is invalid
        ImportError: If constraint provider cannot be imported
    """
    async with _cache_lock:
        # Check cache first
        if not force_rebuild and tenant_id in _solver_cache:
            cache_entry = _solver_cache[tenant_id]

            # Check if cache is still valid (config hasn't changed)
            try:
                current_config = await load_profile(tenant_id)
                config_hash = _hash_config(current_config)

                if cache_entry.get('config_hash') == config_hash:
                    logger.debug(f"Using cached solver factory for tenant {tenant_id}")
                    return cache_entry['factory']
                else:
                    logger.info(f"Config changed for tenant {tenant_id}, rebuilding solver")
            except Exception as e:
                logger.warning(f"Failed to validate cache for tenant {tenant_id}: {e}")

        # Build new factory
        try:
            # Load tenant configuration
            cfg = await load_profile(tenant_id)
            config_hash = _hash_config(cfg)

            # Build constraint providers
            providers = []
            constraint_set = cfg.get('constraint_set', [])
            parameters = cfg.get('parameters', {})

            logger.info(f"Building solver for tenant {tenant_id} with constraints: {constraint_set}")

            for constraint_name in constraint_set:
                try:
                    provider = _load_constraint_provider(constraint_name, parameters)
                    providers.append(provider)
                    logger.debug(f"Loaded constraint provider: {constraint_name}")
                except Exception as e:
                    logger.error(f"Failed to load constraint provider {constraint_name}: {e}")
                    raise ImportError(f"Cannot load constraint provider '{constraint_name}': {e}")

            if not providers:
                raise ValueError(f"No valid constraint providers found for tenant {tenant_id}")

            # Build solver factory with Timefold configuration
            # Create constraint provider function
            def define_constraints(constraint_factory):
                """Combine all constraint providers into a single function."""
                constraints = []
                for provider_func in providers:
                    # Get the constraint creation function
                    constraint_creator = provider_func(cfg.get('parameters', {}))
                    # Call the constraint creator with the factory
                    constraint = constraint_creator(constraint_factory)
                    constraints.append(constraint)
                return constraints

            # Create solver configuration following Timefold best practices
            solver_config = SolverConfig(
                solution_class=AssignmentSolution,
                entity_class_list=[Assignment],
                score_director_factory_config=ScoreDirectorFactoryConfig(
                    constraint_provider_function=define_constraints
                ),
                termination_config=TerminationConfig(
                    spent_limit=Duration(seconds=cfg.get('solver_config', {}).get('time_limit_seconds', 30))
                )
            )

            # Create solver factory
            sf = SolverFactory.create(solver_config)
            logger.info("Created Timefold solver factory with proper configuration")

            # Note: Constraint weights are handled in the constraint provider functions
            # Each constraint uses the weights from the configuration when creating penalties/rewards

            # Cache the factory
            _solver_cache[tenant_id] = {
                'factory': sf,
                'config_hash': config_hash,
                'created_at': time.time()
            }

            logger.info(f"Successfully built and cached solver factory for tenant {tenant_id}")
            return sf

        except Exception as e:
            logger.error(f"Failed to build solver factory for tenant {tenant_id}: {e}")
            raise


def _hash_config(config: Dict[str, Any]) -> str:
    """
    Create a hash of the configuration for cache validation.

    Args:
        config: Configuration dictionary

    Returns:
        SHA256 hash of the configuration
    """
    # Convert config to a stable string representation
    config_str = json.dumps(config, sort_keys=True, default=str)
    return hashlib.sha256(config_str.encode()).hexdigest()


async def invalidate_cache(tenant_id: Optional[str] = None) -> None:
    """
    Invalidate solver factory cache.

    Args:
        tenant_id: Specific tenant to invalidate, or None to clear all
    """
    async with _cache_lock:
        if tenant_id:
            if tenant_id in _solver_cache:
                del _solver_cache[tenant_id]
                logger.info(f"Invalidated cache for tenant {tenant_id}")
        else:
            _solver_cache.clear()
            logger.info("Cleared all solver factory cache")


def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache statistics for monitoring.

    Returns:
        Dictionary with cache statistics
    """
    return {
        'cached_tenants': list(_solver_cache.keys()),
        'cache_size': len(_solver_cache),
        'cache_entries': {
            tenant_id: {
                'created_at': entry['created_at'],
                'age_seconds': time.time() - entry['created_at']
            }
            for tenant_id, entry in _solver_cache.items()
        }
    }


def _load_constraint_provider(constraint_name: str, parameters: Dict[str, Any]):
    """
    Load a constraint provider by name.

    Args:
        constraint_name: Name of the constraint provider
        parameters: Configuration parameters to pass to the provider

    Returns:
        Constraint provider instance

    Raises:
        ImportError: If the provider cannot be imported
        AttributeError: If the provider function is not found
    """
    if constraint_name not in _PROVIDERS:
        raise ImportError(f"Unknown constraint provider: {constraint_name}")

    module_path = _PROVIDERS[constraint_name]

    try:
        # Import the constraint module
        module = importlib.import_module(module_path)

        # Get the constraint function
        function_name = f"{constraint_name}_constraints"
        constraint_function = getattr(module, function_name)

        # Call the function with parameters to get the provider
        provider = constraint_function(parameters)

        return provider

    except ImportError as e:
        raise ImportError(f"Cannot import module {module_path}: {e}")
    except AttributeError as e:
        raise AttributeError(f"Function {function_name} not found in {module_path}: {e}")


# Removed demonstration and weight application functions since we're using pure Timefold


# Weight parsing removed - weights are handled directly in constraint providers


def get_available_constraints() -> List[str]:
    """
    Get list of available constraint providers.

    Returns:
        List of constraint provider names
    """
    return list(_PROVIDERS.keys())


def validate_constraint_configuration(constraint_set: List[str]) -> List[str]:
    """
    Validate a constraint configuration.

    Args:
        constraint_set: List of constraint names to validate

    Returns:
        List of validation errors (empty if valid)
    """
    errors = []

    for constraint_name in constraint_set:
        if constraint_name not in _PROVIDERS:
            errors.append(f"Unknown constraint: {constraint_name}")

    return errors


# Pure async-only implementation - no sync compatibility needed for scratch development
