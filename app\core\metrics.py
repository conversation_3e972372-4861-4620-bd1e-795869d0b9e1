"""
Prometheus metrics for assignment service monitoring.

Provides comprehensive metrics for solver performance, cache efficiency,
constraint violations, and operational health.
"""

import time
import logging
from typing import Dict, Any, Optional
from functools import wraps
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest

logger = logging.getLogger(__name__)

# Create custom registry for the assignment service
assignment_registry = CollectorRegistry()

# Solver Performance Metrics
solver_requests_total = Counter(
    'assignment_solver_requests_total',
    'Total number of solver requests',
    ['tenant_id', 'status'],
    registry=assignment_registry
)

solver_duration_seconds = Histogram(
    'assignment_solver_duration_seconds',
    'Time spent solving assignment problems',
    ['tenant_id'],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0],
    registry=assignment_registry
)

solver_score = Gauge(
    'assignment_solver_score',
    'Final solver score (hard/soft)',
    ['tenant_id', 'score_type'],
    registry=assignment_registry
)

# Cache Performance Metrics
cache_operations_total = Counter(
    'assignment_cache_operations_total',
    'Total cache operations',
    ['operation', 'tenant_id', 'result'],
    registry=assignment_registry
)

cache_size = Gauge(
    'assignment_cache_size',
    'Number of cached entries',
    ['cache_type'],
    registry=assignment_registry
)

cache_age_seconds = Histogram(
    'assignment_cache_age_seconds',
    'Age of cache entries when accessed',
    ['cache_type'],
    buckets=[1, 10, 60, 300, 1800, 3600, 7200, 14400, 28800, 86400],
    registry=assignment_registry
)

# Problem Size Metrics
problem_size = Histogram(
    'assignment_problem_size',
    'Size of assignment problems',
    ['tenant_id', 'dimension'],
    buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500],
    registry=assignment_registry
)

# Constraint Violation Metrics
constraint_violations_total = Counter(
    'assignment_constraint_violations_total',
    'Total constraint violations',
    ['tenant_id', 'constraint_type', 'severity'],
    registry=assignment_registry
)

# Configuration Metrics
config_reloads_total = Counter(
    'assignment_config_reloads_total',
    'Total configuration reloads',
    ['tenant_id', 'reason'],
    registry=assignment_registry
)

config_errors_total = Counter(
    'assignment_config_errors_total',
    'Total configuration errors',
    ['tenant_id', 'error_type'],
    registry=assignment_registry
)

# Resource Utilization Metrics
resource_utilization = Gauge(
    'assignment_resource_utilization',
    'Resource utilization percentage',
    ['tenant_id', 'resource_type'],
    registry=assignment_registry
)

task_assignment_rate = Gauge(
    'assignment_task_assignment_rate',
    'Percentage of tasks successfully assigned',
    ['tenant_id'],
    registry=assignment_registry
)

# System Health Metrics
system_info = Info(
    'assignment_system_info',
    'System information',
    registry=assignment_registry
)

active_tenants = Gauge(
    'assignment_active_tenants',
    'Number of active tenants',
    registry=assignment_registry
)


class MetricsCollector:
    """Centralized metrics collection and reporting."""
    
    def __init__(self):
        self.start_time = time.time()
        self._setup_system_info()
    
    def _setup_system_info(self):
        """Set up static system information."""
        import platform
        import sys
        
        system_info.info({
            'version': '1.0.0',  # Should come from package metadata
            'python_version': sys.version,
            'platform': platform.platform(),
            'architecture': platform.architecture()[0]
        })
    
    def record_solver_request(self, tenant_id: str, duration: float, status: str, 
                            score_hard: int = 0, score_soft: int = 0):
        """Record solver request metrics."""
        solver_requests_total.labels(tenant_id=tenant_id, status=status).inc()
        solver_duration_seconds.labels(tenant_id=tenant_id).observe(duration)
        
        if status == 'success':
            solver_score.labels(tenant_id=tenant_id, score_type='hard').set(score_hard)
            solver_score.labels(tenant_id=tenant_id, score_type='soft').set(score_soft)
    
    def record_cache_operation(self, operation: str, tenant_id: str, result: str, 
                             cache_type: str = 'solver', age_seconds: Optional[float] = None):
        """Record cache operation metrics."""
        cache_operations_total.labels(
            operation=operation, 
            tenant_id=tenant_id, 
            result=result
        ).inc()
        
        if age_seconds is not None:
            cache_age_seconds.labels(cache_type=cache_type).observe(age_seconds)
    
    def update_cache_size(self, cache_type: str, size: int):
        """Update cache size metrics."""
        cache_size.labels(cache_type=cache_type).set(size)
    
    def record_problem_size(self, tenant_id: str, num_tasks: int, num_resources: int, num_consumers: int):
        """Record problem size metrics."""
        problem_size.labels(tenant_id=tenant_id, dimension='tasks').observe(num_tasks)
        problem_size.labels(tenant_id=tenant_id, dimension='resources').observe(num_resources)
        problem_size.labels(tenant_id=tenant_id, dimension='consumers').observe(num_consumers)
    
    def record_constraint_violation(self, tenant_id: str, constraint_type: str, severity: str):
        """Record constraint violation."""
        constraint_violations_total.labels(
            tenant_id=tenant_id,
            constraint_type=constraint_type,
            severity=severity
        ).inc()
    
    def record_config_reload(self, tenant_id: str, reason: str):
        """Record configuration reload."""
        config_reloads_total.labels(tenant_id=tenant_id, reason=reason).inc()
    
    def record_config_error(self, tenant_id: str, error_type: str):
        """Record configuration error."""
        config_errors_total.labels(tenant_id=tenant_id, error_type=error_type).inc()
    
    def update_resource_utilization(self, tenant_id: str, resource_type: str, utilization: float):
        """Update resource utilization metrics."""
        resource_utilization.labels(tenant_id=tenant_id, resource_type=resource_type).set(utilization)
    
    def update_task_assignment_rate(self, tenant_id: str, rate: float):
        """Update task assignment rate."""
        task_assignment_rate.labels(tenant_id=tenant_id).set(rate)
    
    def update_active_tenants(self, count: int):
        """Update active tenant count."""
        active_tenants.set(count)
    
    def get_metrics(self) -> str:
        """Get all metrics in Prometheus format."""
        return generate_latest(assignment_registry).decode('utf-8')


# Global metrics collector instance
metrics = MetricsCollector()


def track_solver_performance(func):
    """Decorator to track solver performance metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        tenant_id = kwargs.get('tenant_id', 'unknown')
        status = 'success'
        score_hard = 0
        score_soft = 0
        
        try:
            result = await func(*args, **kwargs)
            
            # Extract score if available
            if hasattr(result, 'score'):
                score_parts = str(result.score).split('/')
                if len(score_parts) >= 2:
                    score_hard = int(score_parts[0].replace('hard', '').strip())
                    score_soft = int(score_parts[1].replace('soft', '').strip())
            
            return result
            
        except Exception as e:
            status = 'error'
            logger.error(f"Solver error for tenant {tenant_id}: {e}")
            raise
            
        finally:
            duration = time.time() - start_time
            metrics.record_solver_request(tenant_id, duration, status, score_hard, score_soft)
    
    return wrapper


def track_cache_operation(operation: str, cache_type: str = 'solver'):
    """Decorator to track cache operations."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            tenant_id = kwargs.get('tenant_id', args[0] if args else 'unknown')
            start_time = time.time()
            result = 'miss'
            
            try:
                response = await func(*args, **kwargs)
                result = 'hit' if response else 'miss'
                return response
                
            except Exception as e:
                result = 'error'
                logger.error(f"Cache operation error: {e}")
                raise
                
            finally:
                age_seconds = time.time() - start_time
                metrics.record_cache_operation(operation, tenant_id, result, cache_type, age_seconds)
        
        return wrapper
    return decorator


def update_cache_metrics():
    """Update cache size metrics from factory."""
    try:
        from app.solver.factory import get_cache_stats
        stats = get_cache_stats()
        metrics.update_cache_size('solver', stats['cache_size'])
        
        from app.core.config_loader import config_loader
        # Update config cache size if available
        # This would need to be implemented in config_loader
        
    except Exception as e:
        logger.error(f"Failed to update cache metrics: {e}")


def update_system_metrics():
    """Update system-level metrics."""
    try:
        # Update active tenant count
        from app.solver.factory import get_cache_stats
        stats = get_cache_stats()
        metrics.update_active_tenants(len(stats['cached_tenants']))
        
        # Update cache metrics
        update_cache_metrics()
        
    except Exception as e:
        logger.error(f"Failed to update system metrics: {e}")


# Periodic metrics update (would be called by background task)
async def metrics_update_loop(interval: int = 60):
    """Background task to update metrics periodically."""
    import asyncio
    
    while True:
        try:
            update_system_metrics()
            await asyncio.sleep(interval)
        except Exception as e:
            logger.error(f"Error in metrics update loop: {e}")
            await asyncio.sleep(interval)
