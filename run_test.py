"""
Simple test runner to validate the task dependencies scenario.
"""
import pytest
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    # Run specific tests with minimal output
    exit_code = pytest.main([
        "tests/scenarios/task_dependencies/test_task_dependencies.py::TestTaskDependenciesScenario::test_scenario_data_loaded_correctly",
        "-v",
        "--tb=short",
        "--no-header",
        "--disable-warnings"
    ])
    sys.exit(exit_code)
