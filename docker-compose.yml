services:
  db:
    image: postgres:16
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: assignments
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  api:
    build: .
    depends_on:
      - db
    environment:
      DATABASE_URL: postgres+asyncpg://user:password@db:5432/assignments
    volumes:
      - ./app/config_profiles:/configs
    ports:
      - "8000:8000"

volumes:
  db_data:
