
import os
import logging
from typing import AsyncGenerator, Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, DateTime, Boolean, Integer, JSON, text, select
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from fastapi import Depends, HTTPException
from dotenv import load_dotenv

from app.domain.models import Provider, Task, Consumer

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv(
    'DATABASE_URL',
    'postgresql+asyncpg://user:password@localhost:5432/assignments'
)

# Create async engine with connection pooling
engine = create_async_engine(
    DATABASE_URL,
    echo=os.getenv('DB_ECHO', 'false').lower() == 'true',
    future=True,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# Session factory - simple approach for compatibility
def get_async_session() -> AsyncSession:
    """Create a new async session."""
    return AsyncSession(engine, expire_on_commit=False)


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


class ProviderDB(Base):
    """Database model for Providers with RLS support."""
    __tablename__ = "providers"

    id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), primary_key=True)
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    skills: Mapped[List[str]] = mapped_column(JSON, default=list)
    capacity: Mapped[int] = mapped_column(Integer, default=1)
    properties: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TaskDB(Base):
    """Database model for Tasks with RLS support."""
    __tablename__ = "tasks"

    id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), primary_key=True)
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    consumer_id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), nullable=False)
    required_skills: Mapped[List[str]] = mapped_column(JSON, default=list)
    duration_min: Mapped[int] = mapped_column(Integer, nullable=False)
    earliest_start: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    latest_end: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    urgent: Mapped[bool] = mapped_column(Boolean, default=False)
    properties: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ConsumerDB(Base):
    """Database model for Consumers with RLS support."""
    __tablename__ = "consumers"

    id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), primary_key=True)
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    preferences: Mapped[Dict[str, Any]] = mapped_column(JSON, default=dict)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session."""
    session = get_async_session()
    try:
        yield session
    except Exception as e:
        await session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        await session.close()


@asynccontextmanager
async def get_tenant_session(tenant_id: str) -> AsyncGenerator[AsyncSession, None]:
    """Get a database session with RLS context set for the tenant."""
    session = get_async_session()
    try:
        # Set RLS context for this session
        await session.execute(
            text("SELECT set_config('app.current_tenant_id', :tenant_id, true)"),
            {"tenant_id": tenant_id}
        )
        yield session
    except Exception as e:
        await session.rollback()
        logger.error(f"Tenant session error for {tenant_id}: {e}")
        raise
    finally:
        await session.close()


async def fetch_problem_for_tenant(tenant_id: str) -> tuple[List[Provider], List[Task], List[Consumer]]:
    """
    Fetch optimization problem data for a specific tenant.
    Returns tuple of (resources, tasks, consumers) filtered by RLS.
    """
    async with get_tenant_session(tenant_id) as session:
        try:
            # Fetch providers
            resources_result = await session.execute(
                select(ProviderDB).where(ProviderDB.tenant_id == tenant_id)
            )
            resources_db = resources_result.scalars().all()

            # Fetch tasks
            tasks_result = await session.execute(
                select(TaskDB).where(TaskDB.tenant_id == tenant_id)
            )
            tasks_db = tasks_result.scalars().all()

            # Fetch consumers
            consumers_result = await session.execute(
                select(ConsumerDB).where(ConsumerDB.tenant_id == tenant_id)
            )
            consumers_db = consumers_result.scalars().all()

            # Convert to domain models
            resources = [
                Provider(
                    id=r.id,
                    name=r.name,
                    skills=r.skills,
                    capacity=r.capacity,
                    properties=r.properties
                ) for r in resources_db
            ]

            tasks = [
                Task(
                    id=t.id,
                    consumer_id=t.consumer_id,
                    required_skills=t.required_skills,
                    duration_min=t.duration_min,
                    earliest_start=t.earliest_start,
                    latest_end=t.latest_end,
                    urgent=t.urgent,
                    properties=t.properties
                ) for t in tasks_db
            ]

            consumers = [
                Consumer(
                    id=c.id,
                    name=c.name,
                    properties=c.preferences  # Map preferences to properties
                ) for c in consumers_db
            ]

            logger.info(f"Fetched problem for tenant {tenant_id}: "
                       f"{len(resources)} providers, {len(tasks)} tasks, {len(consumers)} consumers")

            return resources, tasks, consumers

        except Exception as e:
            logger.error(f"Error fetching problem for tenant {tenant_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


async def init_database():
    """Initialize database tables and RLS policies."""
    async with engine.begin() as conn:
        # Create tables
        await conn.run_sync(Base.metadata.create_all)

        # Enable RLS on tables
        rls_statements = [
            "ALTER TABLE providers ENABLE ROW LEVEL SECURITY;",
            "ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;",
            "ALTER TABLE consumers ENABLE ROW LEVEL SECURITY;",

            # Create RLS policies
            """
            CREATE POLICY tenant_isolation_providers ON providers
            USING (tenant_id = current_setting('app.current_tenant_id', true));
            """,
            """
            CREATE POLICY tenant_isolation_tasks ON tasks
            USING (tenant_id = current_setting('app.current_tenant_id', true));
            """,
            """
            CREATE POLICY tenant_isolation_consumers ON consumers
            USING (tenant_id = current_setting('app.current_tenant_id', true));
            """
        ]

        for stmt in rls_statements:
            try:
                await conn.execute(text(stmt))
            except Exception as e:
                # Ignore if policy already exists
                if "already exists" not in str(e):
                    logger.warning(f"RLS setup warning: {e}")


async def health_check() -> bool:
    """Check database connectivity."""
    try:
        session = get_async_session()
        try:
            await session.execute(text("SELECT 1"))
            return True
        finally:
            await session.close()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False
