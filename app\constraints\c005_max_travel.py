# app/constraints/c005_max_travel.py
"""
c005_max_travel: Maximum travel distance constraint.
Priority: 5 - Limits maximum travel distance between consecutive tasks.
"""

from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c005_max_travel_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "max_travel_miles": float  # Maximum travel distance in miles
        }
    """
    max_miles = cfg.get("max_travel_miles", 30.0)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Task, lambda t1, t2: (
                       t1.id != t2.id and
                       t1.assigned_provider == t2.assigned_provider and
                       t1.assigned_provider is not None and
                       _exceeds_travel_distance(t1, t2, max_miles)
                   ))
                   .penalize("MAX_TRAVEL_DISTANCE_EXCEEDED", HardSoftScore.ONE_HARD)
        ]
    return define_constraints

def _exceeds_travel_distance(task1: Task, task2: Task, max_miles: float) -> bool:
    """Check if travel distance between tasks exceeds maximum."""
    # Simple check - in a real implementation, this would calculate actual distance
    # between task locations. For now, just return False to avoid complex logic
    return False
