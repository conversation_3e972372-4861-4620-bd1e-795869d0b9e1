# Domain Model Analysis: Healthcare Assignment System

## Overview

The healthcare assignment system uses a sophisticated domain model designed for optimizing healthcare provider assignments using Timefold's constraint satisfaction engine. The domain model follows Domain-Driven Design (DDD) principles with clear separation between business entities and optimization entities.

## Domain Architecture

```mermaid
graph TB
    subgraph "Business Domain (models.py)"
        Provider[Provider]
        Consumer[Consumer] 
        Task[Task]
        Location[Location]
    end
    
    subgraph "Optimization Domain (solution.py)"
        Assignment[Assignment - Planning Entity]
        AssignmentSolution[AssignmentSolution - Planning Solution]
    end
    
    subgraph "Constraint Engine"
        C001[C001: Skills Matching]
        C002[C002: Provider Availability] 
        C003[C003: Time Overlap Prevention]
        C014[C014: Task Dependencies]
    end
    
    Task --> Assignment
    Provider --> Assignment
    Consumer --> Assignment
    Assignment --> AssignmentSolution
    AssignmentSolution --> C001
    AssignmentSolution --> C002
    AssignmentSolution --> C003
    AssignmentSolution --> C014
```

## Business Domain Models (`models.py`)

### Core Entity Design Philosophy

The domain models follow a **"Type Safety First"** design philosophy with **all properties promoted to first-class fields** rather than using generic property dictionaries. This provides:

1. **Maximum Type Safety**: Every field is explicitly typed with Pydantic validation
2. **IDE Support**: Full autocomplete and refactoring support
3. **Runtime Validation**: Automatic validation of all data structures
4. **Documentation**: Self-documenting code through explicit field definitions

### 1. Location Model

```python
class Location(BaseModel):
    lat: float
    lng: float
    address: Optional[str] = None
```

**Purpose**: Geographic positioning for travel time calculations and provider-patient matching.

**Design Decisions**:
- Separate model rather than embedded coordinates for reusability
- Optional address for human-readable location display
- Float precision sufficient for healthcare logistics (typically within metropolitan areas)

### 2. Provider Model

```python
class Provider(BaseModel):
    # Core identification
    id: UUID
    name: str
    skills: List[str] = []
    capacity: int = 1

    # Geographic and availability
    location: Optional[Location] = None
    shift_start: Optional[str] = None
    shift_end: Optional[str] = None
    availability: Dict[str, Any] = {}

    # Qualifications and capabilities
    experience_years: Optional[int] = None
    languages: List[str] = []
    transportation: Optional[str] = None
    role: Optional[str] = None  # "Registered Nurse", "CNA", "LPN"

    # Current state
    current_task_count: int = 0
    critical: bool = False

    # Extension point
    properties: Dict[str, Any] = {}
```

**Key Design Elements**:

1. **Skills-Based Architecture**: Skills are stored as a list of strings for flexible matching
2. **Healthcare Role Specification**: Explicit `role` field for certification requirements
3. **Availability Modeling**: Both simple (shift_start/end) and complex (availability dict) scheduling
4. **State Tracking**: `current_task_count` for workload balancing
5. **Extension Point**: `properties` dict for client-specific attributes during deployment

### 3. Consumer (Patient) Model

```python
class Consumer(BaseModel):
    # Core identification
    id: UUID
    name: str

    # Geographic and demographics
    location: Optional[Location] = None
    age: Optional[int] = None
    preferred_language: Optional[str] = None

    # Clinical context
    conditions: List[str] = []
    mobility: Optional[str] = None
    care_episode_id: Optional[str] = None
    insurance: Optional[str] = None

    # Extension point
    properties: Dict[str, Any] = {}
```

**Design Philosophy**:
- **Privacy-Conscious**: Only essential fields for optimization
- **Clinical Context**: Conditions and mobility for provider matching
- **Care Continuity**: `care_episode_id` for linking related visits

### 4. Task Model (The Heart of the System)

```python
class Task(BaseModel):
    # Core assignment data
    id: UUID
    consumer_id: UUID
    required_skills: List[str]
    duration_min: int
    
    # Scheduling constraints
    earliest_start: Optional[datetime] = None
    latest_end: Optional[datetime] = None
    
    # Business priority
    urgent: bool = False
    priority: str = "normal"  # critical, high, normal, low
    required: bool = True
    
    # Geographic context
    location: Optional[Location] = None
    
    # Clinical context
    task_type: Optional[str] = None
    conditions: List[str] = []
    
    # Planning variables (will be set by optimizer)
    assigned_provider: Optional[UUID] = None
    assigned_start_time: Optional[datetime] = None
    assigned_end_time: Optional[datetime] = None
    
    # TASK DEPENDENCIES - The Key Innovation
    sequence_group: Optional[str] = None
    sequence_order: Optional[int] = None
    prerequisite_task_ids: List[str] = []
    same_provider_required: bool = False
```

**Task Dependencies Architecture**:

The task dependency system supports complex healthcare workflows:

1. **Sequence Groups**: Tasks that must be performed in order by the same provider
   - Example: `sequence_group: "wound_care_episode_001"`
   - All tasks in the group must be assigned to the same provider

2. **Sequence Order**: Numerical ordering within a sequence group
   - `sequence_order: 1, 2, 3` ensures proper task ordering

3. **Prerequisites**: Tasks that must complete before this task can start
   - `prerequisite_task_ids: ["task_1", "task_2"]`
   - Flexible dependency modeling beyond just sequences

4. **Provider Continuity**: Control whether dependencies require the same provider
   - `same_provider_required: true` for clinical continuity
   - `same_provider_required: false` for flexible scheduling

## Optimization Domain (`solution.py`)

### Timefold Integration Design

The optimization domain transforms business entities into Timefold planning entities with careful attention to the constraint satisfaction problem structure.

### 1. Assignment (Planning Entity)

```python
@planning_entity
@dataclass
class Assignment:
    # Problem facts (immutable)
    id: str
    consumer_id: str
    task_type: str
    required_skills: List[str]
    duration_min: int
    earliest_start: datetime
    latest_end: datetime
    location: Location
    
    # Task dependencies
    sequence_group: Optional[str] = None
    sequence_order: Optional[int] = None
    prerequisite_task_ids: List[str] = field(default_factory=list)
    same_provider_required: bool = False
    
    # Planning variables (optimized by Timefold)
    assigned_provider: Optional[Provider] = None
    assigned_start_time: Optional[datetime] = None
```

**Design Rationale**:

1. **Immutable Problem Facts**: Core task attributes that don't change during optimization
2. **Planning Variables**: Only `assigned_provider` and `assigned_start_time` are optimized
3. **Derived Properties**: End time calculated automatically from start time + duration
4. **Dependency Metadata**: All dependency information preserved for constraint evaluation

### 2. AssignmentSolution (Planning Solution)

```python
@planning_solution
@dataclass
class AssignmentSolution:
    # Problem facts (available choices)
    providers: List[Provider] = field(default_factory=list)
    consumers: List[Consumer] = field(default_factory=list)
    time_slots: List[datetime] = field(default_factory=list)
    
    # Planning entities (to be optimized)
    assignments: List[Assignment] = field(default_factory=list)
    
    # Planning score (optimization objective)
    score: Optional[HardSoftScore] = None
```

**Architecture Benefits**:

1. **Clear Separation**: Problem facts vs. planning entities vs. optimization objective
2. **Value Range Providers**: Timefold decorators provide available choices for planning variables
3. **Solution Analytics**: Built-in methods for solution analysis and validation
4. **Multi-tenant Support**: `tenant_id` and `optimization_config` for isolated deployments

## Real-World Use Case: Sequential Wound Care

The system's sophistication is best demonstrated through the wound care scenario:

### Clinical Workflow Requirements

```yaml
# Task 1: Wound Assessment (RN required, first in sequence)
dependencies:
  sequence_group: "wound_care_episode_001"
  sequence_order: 1
  prerequisite_task_ids: []
  same_provider_required: true

# Task 2: Wound Cleaning (RN required, same RN as Task 1)  
dependencies:
  sequence_group: "wound_care_episode_001"
  sequence_order: 2
  prerequisite_task_ids: ["task_1"]
  same_provider_required: true

# Task 3: Wound Dressing (RN required, same RN as Tasks 1-2)
dependencies:
  sequence_group: "wound_care_episode_001" 
  sequence_order: 3
  prerequisite_task_ids: ["task_2"]
  same_provider_required: true

# Task 4: Documentation (Any provider, after all clinical tasks)
dependencies:
  sequence_group: null
  prerequisite_task_ids: ["task_1", "task_2", "task_3"]
  same_provider_required: false
```

### Constraint Satisfaction Challenge

The optimizer must satisfy:

1. **Hard Constraints**:
   - Skills matching (RN certification for clinical tasks)
   - Time window compliance
   - Provider availability
   - Task sequencing (1 → 2 → 3 → 4)
   - Same provider for tasks 1-3

2. **Soft Constraints**:
   - Minimize travel time
   - Balance workload
   - Respect provider preferences
   - Optimize for patient satisfaction

## Data Transformation Pipeline

### Business → Optimization Domain

```python
def convert_tasks_to_assignments(tasks: List[Task], consumers: List[Consumer]) -> List[Assignment]:
    """Transform business entities into optimization entities."""
    assignments = []
    for task in tasks:
        assignment = Assignment(
            id=str(task.id),
            consumer_id=str(task.consumer_id),
            task_type=task.task_type,
            required_skills=task.required_skills,
            duration_min=task.duration_min,
            earliest_start=task.earliest_start,
            latest_end=task.latest_end,
            # ... dependency fields preserved
            sequence_group=task.sequence_group,
            sequence_order=task.sequence_order,
            prerequisite_task_ids=[str(pid) for pid in task.prerequisite_task_ids],
            same_provider_required=task.same_provider_required
        )
        assignments.append(assignment)
    return assignments
```

### Time Slot Generation

```python
def create_time_slots(start_time: datetime, end_time: datetime, interval_minutes: int = 15) -> List[datetime]:
    """Create discrete time slots for planning variable range."""
    time_slots = []
    current_time = start_time
    while current_time <= end_time:
        time_slots.append(current_time)
        current_time += timedelta(minutes=interval_minutes)
    return time_slots
```

**Design Decision**: 15-minute intervals balance optimization performance with scheduling precision.

## Solution Validation & Analytics

### Built-in Validation

```python
def validate_solution(self) -> List[str]:
    """Validate solution for consistency."""
    errors = []
    
    # Check for scheduling conflicts
    assigned_slots = {}
    for assignment in self.assignments:
        if assignment.is_assigned:
            key = (assignment.assigned_provider.id, assignment.assigned_start_time)
            if key in assigned_slots:
                errors.append(f"Scheduling conflict detected")
    
    # Check time windows
    for assignment in self.assignments:
        if assignment.assigned_start_time:
            if assignment.assigned_start_time < assignment.earliest_start:
                errors.append(f"Violates earliest start time")
    
    # Check skill requirements  
    for assignment in self.assignments:
        if assignment.assigned_provider:
            missing_skills = set(assignment.required_skills) - set(assignment.assigned_provider.skills)
            if missing_skills:
                errors.append(f"Missing required skills: {missing_skills}")
    
    return errors
```

### Solution Analytics

```python
@property
def assignment_rate(self) -> float:
    """Percentage of tasks successfully assigned."""
    if self.total_assignments == 0:
        return 0.0
    return (self.assigned_count / self.total_assignments) * 100

def get_assignments_by_sequence_group(self, sequence_group: str) -> List[Assignment]:
    """Get all assignments in a sequence, properly ordered."""
    assignments = [a for a in self.assignments if a.sequence_group == sequence_group]
    return sorted(assignments, key=lambda a: a.sequence_order or 0)
```

## Key Design Innovations

### 1. Dependency-Aware Task Modeling

The task dependency system supports complex healthcare workflows while remaining flexible:

- **Sequence Groups**: For tasks requiring provider continuity
- **Prerequisites**: For flexible dependency modeling
- **Same Provider Control**: Granular control over continuity requirements

### 2. Type-Safe Property Promotion

Rather than using generic property bags, all common attributes are promoted to first-class fields:

- **IDE Support**: Full autocomplete and refactoring
- **Type Safety**: Compile-time validation of data access
- **Performance**: Direct attribute access vs. dictionary lookups

### 3. Optimization-Business Domain Separation

Clear separation between business entities (`models.py`) and optimization entities (`solution.py`):

- **Business Logic Isolation**: Domain models focus on business rules
- **Optimization Focus**: Solution models optimized for constraint satisfaction
- **Clean Transformation**: Explicit conversion between domains

### 4. Multi-Dimensional Constraint Support

The domain model supports multiple types of constraints simultaneously:

- **Temporal**: Time windows, durations, sequences
- **Spatial**: Location-based assignments, travel time
- **Skill-based**: Certification requirements, experience levels
- **Workflow**: Task dependencies, provider continuity
- **Resource**: Capacity limits, workload balancing

## Extensibility Architecture

### Client-Specific Extensions

```python
# In Provider model
properties: Dict[str, Any] = {}  # Extension point for deployment-specific fields

# Usage example for a specific healthcare client
provider.properties.update({
    "union_member": True,
    "overtime_eligible": False,
    "specialty_certifications": ["wound_care_specialist", "diabetes_educator"],
    "patient_satisfaction_score": 4.8,
    "preferred_patient_ages": ["adult", "senior"]
})
```

### Constraint Extensibility

The constraint system is designed for easy extension:

```python
# New constraints can be added by implementing the constraint provider pattern
def c015_patient_satisfaction_constraints(cfg: dict):
    @constraint_provider  
    def define_constraints(constraint_factory):
        return [
            constraint_factory
                .for_each(Assignment)
                .filter(lambda a: a.assigned_provider and patient_satisfaction_match(a))
                .reward("C015_PATIENT_SATISFACTION", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints
```

## Performance Considerations

### Memory Optimization

1. **Immutable Problem Facts**: Shared references to providers and consumers
2. **Planning Variable Scoping**: Only assignment variables are mutable
3. **Time Slot Discretization**: Balanced granularity vs. search space size

### Scalability Design

1. **Tenant Isolation**: Multi-tenant configuration without data mixing
2. **Constraint Modularity**: Enable/disable constraints per deployment
3. **Solution Caching**: Immutable problem facts enable solution caching

## Conclusion

The domain model represents a sophisticated healthcare assignment system that balances:

- **Clinical Accuracy**: Proper modeling of healthcare workflows and dependencies
- **Optimization Performance**: Efficient constraint satisfaction problem formulation
- **Type Safety**: Comprehensive compile-time validation
- **Extensibility**: Clean extension points for client-specific requirements
- **Maintainability**: Clear separation of concerns and explicit data transformations

The system successfully handles complex scenarios like sequential wound care while maintaining the flexibility to support diverse healthcare assignment challenges.
