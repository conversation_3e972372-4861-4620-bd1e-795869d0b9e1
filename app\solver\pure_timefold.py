"""
Pure Timefold Integration
========================

Direct Timefold integration following the official quickstart pattern.
No fallback logic - assumes Timefold is available and working.
"""

# Direct imports following Timefold quickstart pattern
from timefold.solver import SolverManager, SolverFactory
from timefold.solver.config import (
    SolverConfig, 
    ScoreDirectorFactoryConfig,
    TerminationConfig, 
    Duration
)
from timefold.solver.domain import (
    planning_entity, 
    planning_solution,
    PlanningVariable,
    PlanningScore,
    ValueRangeProvider,
    PlanningEntityCollectionProperty,
    ProblemFactCollectionProperty
)
from timefold.solver.score import HardSoftScore

from app.domain.solution import AssignmentSolution, Assignment


def create_solver_config(constraint_provider_function, time_limit_seconds=30):
    """Create solver configuration following Timefold pattern."""
    return SolverConfig(
        solution_class=AssignmentSolution,
        entity_class_list=[Assignment],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=constraint_provider_function
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=time_limit_seconds)
        )
    )


def create_solver_manager(constraint_provider_function, time_limit_seconds=30):
    """Create solver manager following Timefold pattern."""
    solver_config = create_solver_config(constraint_provider_function, time_limit_seconds)
    solver_factory = SolverFactory.create(solver_config)
    return SolverManager.create(solver_factory)


# Example usage exactly like Timefold quickstart
def create_healthcare_solver(constraint_provider_function):
    """Create healthcare solver exactly like Timefold examples."""
    return create_solver_manager(constraint_provider_function, time_limit_seconds=30)
