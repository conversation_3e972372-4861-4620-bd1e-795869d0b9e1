# app/constraints/c010_provider_preference.py
"""
c010_provider_preference: Provider preference constraint.
Priority: 10 - Honor resource's own preferences (e.g., "no visits after 6 pm").
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task, Provider

def c010_provider_preference_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "max_end_time": "18:00"
        }
    """
    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Provider, lambda t, p: t.assigned_provider == p.id)
                   .filter(lambda t, p:
                       # Example: if provider.prefer_no_after and task.latest_end > provider.prefer_no_after
                       p.properties.get("prefer_no_after") and t.latest_end and t.latest_end.hour > int(p.properties["prefer_no_after"].split(":")[0])
                   )
                   .penalize("PROVIDER_PREFERENCE_VIOLATION", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints
