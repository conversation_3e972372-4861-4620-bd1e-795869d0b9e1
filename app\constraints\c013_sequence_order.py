"""
Task Sequence Constraint (c013)
Enforces strict task execution sequence within care episodes.
"""

from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.solution import Assignment


def c013_sequence_order_constraints(cfg: dict):
    """
    cfg: dict with optional parameters for task sequence configuration.
    """
    @constraint_provider
    def define_constraints(constraint_factory):
        """
        Enforce task execution sequence within care episodes.

        For now, this is a placeholder constraint that will be implemented
        when the full Timefold constraint API is available.
        """
        # Placeholder constraint - tasks with sequence dependencies should be handled properly
        return [
            constraint_factory
                .for_each(Assignment)
                .filter(lambda assignment: assignment.sequence_group is not None and assignment.sequence_order is not None)
                .filter(lambda assignment: assignment.assigned_provider is None)  # Unassigned tasks in sequence
                .penalize("TASK_SEQUENCE_ORDER_REQUIRED", HardSoftScore.ONE_HARD)
        ]

    return define_constraints
