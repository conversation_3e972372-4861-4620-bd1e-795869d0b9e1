# app/constraints/c002_provider_avail.py
"""
c002_provider_avail: Provider availability constraint.
Priority: 2 - Ensures tasks are only assigned when providers are available.
This is a hard constraint that prevents scheduling conflicts.
"""

from timefold.solver.score import constraint_provider, HardSoftScore
from app.domain.models import Task, Provider

def c002_provider_avail_constraints(cfg):
    """
    Provider availability constraint provider factory.

    Args:
        cfg: Configuration parameters dictionary containing:
            - min_break_min: int (default: 30)

    Returns:
        Constraint provider function
    """
    # Configuration parameters (currently unused but available for future enhancements)    @constraint_provider
    def define_constraints(constraint_factory):
        """Define provider availability constraints."""
        constraints = []

        # Hard constraint: Task starts before resource is available
        constraints.append(
            constraint_factory
                .for_each(Task)
                .join(Provider, lambda t, p: t.assigned_provider == p.id)
                .filter(lambda t, p: t.earliest_start and p.shift_start and
                       t.earliest_start.time() < _parse_time(p.shift_start))
                .penalize("PROVIDER_SHIFT_START_VIOLATION", HardSoftScore.ONE_HARD)
        )

        # Hard constraint: Task ends after resource is no longer available
        constraints.append(
            constraint_factory
                .for_each(Task)
                .join(Provider, lambda t, p: t.assigned_provider == p.id)
                .filter(lambda t, p: t.latest_end and p.shift_end and
                       t.latest_end.time() > _parse_time(p.shift_end))
                .penalize("PROVIDER_SHIFT_END_VIOLATION", HardSoftScore.ONE_HARD)
        )

        return constraints

    return define_constraints

def _parse_time(time_str):
    """Parse time string like '08:00' to time object."""
    if not time_str:
        return None
    try:
        from datetime import time
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)
    except (ValueError, AttributeError):
        return None

# Simplified constraint logic implemented above - removed unused helper functions
