# app/constraints/c011_travel_optimization.py
"""
c011_travel_optimization: Travel time optimization.
Priority: 11 - Minimize actual drive time gaps between consecutive visits for a resource.
This boosting helps cluster appointments geographically.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c011_travel_optimization_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "critical_gap_minutes": int  # threshold for high penalty
        }
    """
    threshold = cfg.get("critical_gap_minutes", 15)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(
                       Task,
                       lambda t1, t2: (t1.assigned_provider == t2.assigned_provider)
                                   and (t1.id != t2.id)
                                   and _tasks_close_in_distance(t1, t2, threshold)
                   )
                   .penalize("MINIMIZE_TRAVEL_TIME", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints

def _tasks_close_in_distance(task1: Task, task2: Task, threshold: int) -> bool:
    """Check if two tasks are close in distance."""
    # Simple check - in a real implementation, this would calculate actual distance
    # between task locations. For now, just return False to avoid complex logic
    return False
