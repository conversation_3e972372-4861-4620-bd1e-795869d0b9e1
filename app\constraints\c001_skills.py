
# app/constraints/c001_skills.py
"""
c001_skills: Skill-matching hard constraint. Resource must have every skill required by the Task.
Priority: 1 (Highest) - Critical safety requirement
"""
from timefold.solver.score import constraint_provider, HardSoftScore
from app.domain.solution import Assignment

def c001_skills_constraints(cfg: dict):
    """
    Create skills constraint that ensures providers have required skills.

    Args:
        cfg: dict with optional parameters (e.g. skill hierarchy), currently not used.

    Returns:
        Constraint function for Timefold solver.
    """
    @constraint_provider
    def create_constraint(constraint_factory):        return (constraint_factory
                .for_each(Assignment)
                .filter(lambda assignment: assignment.assigned_provider is not None)
                .filter(lambda assignment: not set(assignment.required_skills).issubset(set(assignment.assigned_provider.skills)))
                .penalize(HardSoftScore.ONE_HARD)
                .as_constraint("SKILL_MATCH_REQUIRED"))

    return create_constraint
