# app/constraints/c004_visit_completion.py
"""
c004_visit_completion: Visit completion constraint.
Priority: 4 - Ensures all required tasks are assigned and completed within time windows.
"""

from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c004_visit_completion_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "completion_buffer_minutes": int  # Buffer time for completion
        }
    """
    buffer = cfg.get("completion_buffer_minutes", 15)

    @constraint_provider
    def define_constraints(factory):
        return [
            # Hard constraint: Required tasks must be assigned
            factory.for_each(Task)
                   .filter(lambda t: t.required and t.assigned_provider is None)
                   .penalize("REQUIRED_TASK_UNASSIGNED", HardSoftScore.ONE_HARD),
            
            # Hard constraint: Tasks must finish within time window
            factory.for_each(Task)
                   .filter(lambda t: _violates_time_window(t, buffer))
                   .penalize("TASK_TIME_WINDOW_VIOLATION", HardSoftScore.ONE_HARD)
        ]
    return define_constraints

def _violates_time_window(task: Task, buffer_minutes: int) -> bool:
    """Check if task violates its time window."""
    if not task.assigned_provider or not task.earliest_start or not task.latest_end:
        return False

    # For simplicity, check if task duration fits in the time window with buffer
    if task.duration_min:
        window_minutes = (task.latest_end - task.earliest_start).total_seconds() / 60
        return window_minutes < (task.duration_min + buffer_minutes)

    return False
