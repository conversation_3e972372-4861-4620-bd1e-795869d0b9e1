
# Assignment Service

A comprehensive multi-tenant assignment optimization service using Timefold-Py for constraint-based scheduling and resource allocation.

## Overview

The Assignment Service is a sophisticated optimization platform designed specifically for home healthcare organizations, providing intelligent assignment of nursing visits, therapy sessions, and personal care tasks to healthcare providers while respecting complex care requirements. Built on FastAPI with Timefold-Py as the optimization engine, it optimizes for patient outcomes, provider satisfaction, and operational efficiency.

## Key Features

- **Multi-Tenant Architecture**: Complete tenant isolation using PostgreSQL Row-Level Security (RLS)
- **Auto-Discovery Constraint System**: 12 configurable constraint providers with automatic discovery
- **Performance Optimized**: Solver factory caching reduces response time by ~400ms
- **Hot-Reload Configuration**: Runtime configuration changes without service restarts
- **Comprehensive API**: RESTful endpoints for solving, configuration, and monitoring
- **Advanced Constraint Quality**: Exponential decay modeling and travel time integration
- **Operational Monitoring**: Prometheus metrics and comprehensive health checks
- **Production Ready**: Comprehensive logging, error handling, and monitoring

## How It Works

The Assignment Service uses a sophisticated multi-layered architecture with tenant isolation and constraint-based optimization:

```
┌─────────────────────────┐       ┌───────────────┐       ┌─────────────────┐
│   FastAPI / Router      │ <──── │ Config Loader │ <──── │ /configs/*.yml  │
│  (Multi-tenant API)     │       │ (Hot-reload)  │       │ (Business rules)│
└─────────────────────────┘       └───────────────┘       └─────────────────┘
          │                                                         │
          │ HTTP Request                                            │ YAML Config
          │ (X-Tenant-ID)                                          │ Load
          ▼                                                         ▼
┌─────────────────────────┐        cached factory        ┌──────────────────┐
│    SolverFactory        │ ◄─────────────────────────── │   Constraint     │
│  (Tenant-specific)      │        (in memory)          │    Modules       │
│  - Timefold Engine      │                             │  (Auto-discovery)│
│  - Constraint Providers │                             │  - skills.py     │
│  - Optimization Config  │                             │  - urgency.py    │
└─────────────────────────┘                             │  - travel.py     │
          │                                             │  - etc...        │
          │ Solve Request                               └──────────────────┘
          ▼                                                         │
┌─────────────────────────┐  (RLS ensures)             ┌──────────────────┐
│ SQLAlchemy + RLS DB     │ ◄─────────────────────────► │  PostgreSQL DB   │
│ - Tenant Isolation      │   tenant isolation         │  - Row Level Sec │
│ - Domain Models         │                             │  - Multi-tenant  │
│ - Task/Resource/Consumer│                             │  - ACID Compliant│
└─────────────────────────┘                             └──────────────────┘
```

### Key Components:

1. **FastAPI Router**: Multi-tenant API with automatic tenant detection via headers
2. **Config Loader**: Hot-reloadable YAML configurations for business rules
3. **SolverFactory**: Cached Timefold optimization engines per tenant
4. **Constraint Modules**: Auto-discovered pluggable business rules
5. **RLS Database**: PostgreSQL with Row-Level Security for tenant isolation

## Architecture

```
assignment-service/
├─ app/
│  ├─ api/                 # FastAPI routers & dependency injection
│  ├─ core/                # Cross-cutting concerns (DB, RLS, config)
│  ├─ domain/              # Immutable domain models
│  ├─ constraints/         # Pluggable constraint providers
│  ├─ solver/              # Timefold solver factory
│  └─ config_profiles/     # Tenant-specific YAML configurations
├─ tests/                  # Comprehensive test suite
├─ pyproject.toml          # Python project configuration
└─ README.md
```

## Domain Models

The service operates on three core immutable models:

### Resource
Represents available capacity (e.g., nurses, technicians, vehicles):
```python
Resource(
    id=UUID,
    name="Nurse Alice",
    skills=["RN", "wound_care"],
    capacity=1,
    properties={"experience_level": "senior"}
)
```

### Task
Represents work to be assigned:
```python
Task(
    id=UUID,
    consumer_id=UUID,
    required_skills=["RN"],
    duration_min=60,
    earliest_start=datetime,
    latest_end=datetime,
    urgent=False,
    properties={"task_type": "medication"}
)
```

### Consumer
Represents the entity receiving service:
```python
Consumer(
    id=UUID,
    name="Patient Smith",
    properties={"preferred_times": [{"start": 9, "end": 12}]}
)
```

## Constraint Providers

The system includes 12 pluggable constraint providers:

### Hard Constraints (Must be satisfied)
- **skills**: Ensures tasks are assigned to resources with required skills
- **provider_avail**: Checks resource availability during task times
- **overlap**: Prevents overlapping tasks for the same resource
- **visit_completion**: Ensures all required visits are completed
- **max_travel**: Limits maximum travel distance/time between tasks
- **breaks**: Enforces break requirements between tasks

### Soft Constraints (Preferences to optimize)
- **continuity**: Promotes continuity of care with same providers
- **patient_pref**: Considers patient preferences for providers and timing
- **provider_pref**: Considers provider preferences for tasks and schedules
- **travel_time**: Optimizes travel time and routing efficiency
- **workload_balance**: Balances workload among resources
- **urgency**: Prioritizes urgent tasks appropriately

## Configuration Profiles

Tenant-specific behavior is controlled through YAML configuration profiles designed specifically for home healthcare services:

```yaml
profile: "skilled_nursing_constraints"
constraint_set:
  - skills
  - provider_avail
  - overlap
  - visit_completion
  - max_travel
  - breaks
  - continuity
  - patient_pref
  - provider_pref
  - urgency

parameters:
  max_travel_miles: 35
  min_break_min: 30
  urgent_decay_hours: 2

weights:
  SKILL_MATCH: "hard:2000000"
  PROVIDER_AVAIL: "hard:2000000"
  CONTINUITY_OF_CARE: "soft:300"
  URGENT_PRIORITY: "soft:400"
```

### Available Home Healthcare Business Profiles

The service includes three pre-configured profiles tailored for specific home healthcare business models and their unique operational requirements:

#### **skilled_nursing_constraints.yml** - Skilled Nursing Services
- **Business Model**: Licensed nursing care requiring RN/LPN providers
- **Services**: Wound care, medication management, IV therapy, post-acute care
- **Key Priorities**: Clinical compliance, regulatory requirements, patient safety
- **Travel**: Up to 35 miles (skilled nurses cover larger territories)
- **Constraints**: Strict skill matching, continuity for complex treatments, emergency response
- **Optimization**: 120 seconds for complex medical scheduling
- **Best For**: Home health agencies, skilled nursing providers, post-acute care

#### **hospital@home_constraints.yml** - Hospital-Level Care at Home
- **Business Model**: Intensive monitoring and hospital-equivalent care delivery
- **Services**: 24/7 monitoring, ventilator care, complex IV therapy, acute care
- **Key Priorities**: Rapid response, coordinated care teams, hospital standards
- **Travel**: Up to 15 miles (rapid response requirements)
- **Constraints**: Advanced skills, 24/7 coverage, team coordination, emergency response
- **Optimization**: 180 seconds for comprehensive care coordination
- **Best For**: Hospital@home programs, acute care at home, intensive monitoring

#### **pcs_constraints.yml** - Personal Care Services
- **Business Model**: Activities of daily living and companion care
- **Services**: Bathing, meal prep, medication reminders, transportation, companionship
- **Key Priorities**: Relationship-based care, patient comfort, caregiver satisfaction
- **Travel**: Up to 25 miles (cost-effective coverage)
- **Constraints**: Strong continuity, cultural matching, patient/provider preferences
- **Optimization**: 90 seconds for relationship-focused scheduling
- **Best For**: Personal care agencies, companion care, ADL assistance, Medicaid waiver services

### Business Model Alignment

Each configuration is optimized for specific home healthcare business challenges:

```
Clinical Complexity → Care Intensity → Relationship Focus
        ↓                   ↓               ↓
Skilled Nursing → Hospital@Home → Personal Care
        ↓                   ↓               ↓
Compliance Focus → Emergency Response → Comfort Focus
```

### Configuration Customization by Market

Each profile can be adapted for different markets and populations:

```yaml
# Rural market adaptation:
parameters:
  max_travel_miles: 50              # Longer distances in rural areas
  emergency_travel_multiplier: 2.0  # More flexibility for emergencies

# Urban market adaptation:
parameters:
  max_travel_miles: 15              # Dense urban coverage
  cultural_matching_weight: 40      # High diversity populations

# Medicaid waiver services:
parameters:
  continuity_enabled: true          # Relationship-based care
  patient_preference_weight: 35     # Consumer choice emphasis
```

## Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL 12+
- Timefold-Py

### Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd assignment-service
```

2. **Install dependencies using Poetry**:
```bash
# Install Poetry if not already installed
pip install poetry

# Install project dependencies
poetry install

# Activate virtual environment
poetry shell
```

**Alternative: Install with pip**:
```bash
pip install -e .
```

3. **Set up environment variables**:
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your configuration
# Key variables:
# DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/assignments
# CONFIG_DIR=/path/to/configs
# DB_ECHO=false
```

4. **Initialize the database**:
```python
from app.core.db import init_database
await init_database()
```

5. **Start the service**:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### Basic Usage

1. **Health Check**:
```bash
curl http://localhost:8000/solver/health
```

2. **List Available Constraints**:
```bash
curl http://localhost:8000/solver/constraints
```

3. **Solve an Assignment Problem**:
```bash
# Solve a schedule for tenant "skilled_nursing_constraints"
curl -X POST "http://localhost:8000/api/solve/skilled_nursing_constraints" \
  -H "X-Tenant-ID: skilled_nursing_constraints" \
  -H "Content-Type: application/json" \
  --data '{
    "dryRun": true,
    "time_limit_seconds": 120,
    "use_mock_data": true
  }'
```

4. **Get Configuration for a Tenant**:
```bash
curl -X GET "http://localhost:8000/api/config/skilled_nursing_constraints" \
  -H "X-Tenant-ID: skilled_nursing_constraints"
```

5. **Update Configuration (Hot-reload)**:
```bash
curl -X PUT "http://localhost:8000/api/config/skilled_nursing_constraints" \
  -H "X-Tenant-ID: skilled_nursing_constraints" \
  -H "Content-Type: application/json" \
  --data '{
    "parameters": {
      "max_travel_miles": 40,
      "urgent_decay_hours": 3
    }
  }'
```

### Rate Limits and Performance

- **Solve Endpoint**: 10 requests per minute per tenant
- **Config Endpoints**: 60 requests per minute per tenant
- **Health/Status**: No rate limits
- **Typical Response Times**:
  - Health check: < 50ms
  - Configuration: < 100ms
  - Solve (120s limit): 30-120 seconds depending on problem size

## How to Add a New Constraint

Adding a new constraint is straightforward with the auto-discovery system. Follow this template:

### 1. Create the Constraint Module

Create a new file in `app/constraints/` (e.g., `app/constraints/my_new_constraint.py`):

```python
# app/constraints/my_new_constraint.py
"""
Description of what this constraint does.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task, Resource

def my_new_constraint_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "my_parameter": int,  # Description of parameter
            "my_threshold": float  # Another parameter
        }
    """
    threshold = cfg.get("my_threshold", 0.5)

    @constraint_provider
    def define_constraints(factory):
        return [
            # Example: Penalize tasks that violate some business rule
            factory.for_each(Task)
                   .filter(lambda t: _violates_my_rule(t, threshold))
                   .penalize("MY_NEW_CONSTRAINT", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints

def _violates_my_rule(task: Task, threshold: float) -> bool:
    """Helper function to check if task violates the rule."""
    # Your business logic here
    return task.properties.get('some_value', 0) > threshold
```

### 2. Add to Configuration Profile

Update your YAML configuration file (e.g., `app/config_profiles/skilled_nursing_constraints.yml`):

```yaml
constraint_set:
  - skills
  - provider_avail
  - overlap
  - my_new_constraint  # Add your constraint here

parameters:
  my_threshold: 0.8      # Configure your parameters
  my_parameter: 10

weights:
  MY_NEW_CONSTRAINT: "soft:150"  # Set the weight
```

### 3. Test Your Constraint

The constraint will be automatically discovered and loaded. Test it:

```bash
# Check if constraint is discovered
curl http://localhost:8000/solver/constraints

# Test with your configuration
curl -X POST "http://localhost:8000/api/solve/your_tenant" \
  -H "X-Tenant-ID: your_tenant" \
  -H "Content-Type: application/json" \
  --data '{"dryRun": true, "use_mock_data": true}'
```

### 4. Constraint Types

Choose the appropriate constraint type:

- **Hard Constraints**: Must be satisfied (use `HardSoftScore.ONE_HARD`)
  - Skills matching, availability, no overlaps
- **Soft Constraints**: Preferences to optimize (use `HardSoftScore.ONE_SOFT`)
  - Travel time, workload balance, preferences

### 5. Common Patterns

```python
# Filter and penalize pattern
factory.for_each(Task)
       .filter(lambda t: some_condition(t))
       .penalize("CONSTRAINT_NAME", HardSoftScore.ONE_SOFT)

# Join two entities pattern
factory.for_each(Task)
       .join(Resource, lambda t, r: t.assigned_resource == r.id)
       .filter(lambda t, r: some_condition(t, r))
       .penalize("CONSTRAINT_NAME", HardSoftScore.ONE_SOFT)

# Group by and count pattern
factory.for_each(Task)
       .group_by(lambda t: t.assigned_resource, lambda t: 1)
       .filter(lambda resource_id, count: count > max_tasks)
       .penalize("CONSTRAINT_NAME", HardSoftScore.ONE_SOFT)
```

That's it! The constraint will be automatically discovered and integrated into the system.

## 📚 **Documentation**

### **📖 Complete Guide**
**[📋 Assignment Service Guide](docs/ASSIGNMENT_SERVICE_GUIDE.md)** - Everything you need to know about the system:
- **What it does** and **how it works**
- **Timefold concepts** explained with diagrams
- **Healthcare-specific features** and business models
- **Technical implementation** details
- **How to extend** and maintain the system

### **API Documentation**
- **Interactive API Docs**: Available at `/docs` when running the service
- **Configuration Examples**: See `app/config_profiles/` for business model configs
- **Constraint Details**: See individual constraint files in `app/constraints/`

## 🏆 **Project Status**

**The Assignment Service is production-ready and delivers immediate value to home healthcare organizations seeking to optimize their operations while improving patient care quality.**

For detailed project status, build results, and deployment information, see the **[docs/](docs/)** folder.

## 🧪 **Test Results**

**Comprehensive testing with realistic healthcare scenarios demonstrates production readiness:**

- **[Test Results Summary](tests/TEST_RESULTS_SUMMARY.md)** - Complete test execution analysis
- **[NYC Healthcare Test Data](tests/nyc_healthcare_test_data.json)** - Realistic test scenario data
- **[Realistic Scenario Test](tests/realistic_healthcare_scenario.py)** - Live test with real Timefold integration

**Test Coverage**: 5 clinicians, 5 patients, 5 care assignments across NYC with real constraint validation.
