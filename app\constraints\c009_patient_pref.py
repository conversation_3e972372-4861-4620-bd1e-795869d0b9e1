# app/constraints/c009_patient_pref.py
"""
c009_patient_pref: Patient preference constraint.
Priority: 9 - Reward if consumer's preferences (e.g., "no cats," "female caregiver," or time-of-day window) are honored.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task, Provider, Consumer

def c009_patient_pref_constraints(cfg: dict):
    """
    Args:
        cfg: {
            # optional: "preferred_shift": ["morning", "afternoon"]
        }
    """
    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Provider, lambda t, p: t.assigned_provider == p.id)
                   .join(Consumer, lambda t, p, c: c.id == t.consumer_id)
                   .filter(lambda t, p, c: c.properties.get("gender") != p.properties.get("gender"))
                   .penalize("PATIENT_PREFERENCE_VIOLATION", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints


