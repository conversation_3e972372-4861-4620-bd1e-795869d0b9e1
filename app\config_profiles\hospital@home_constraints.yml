# =============================================================================
# HOSPITAL@HOME CONFIGURATION
# =============================================================================
# Healthcare Service: Hospital-level care at home with intensive monitoring
# Focus: Rapid response, 24/7 coverage, coordinated care teams
# =============================================================================

# Profile metadata
profile:
  name: "hospital@home_constraints"
  description: "Hospital-level care at home with intensive monitoring and rapid response capabilities"
  healthcare_service: "hospital_at_home"
  regulatory_level: "critical"
  clinical_complexity: "intensive"

# =============================================================================
# CONSTRAINT CONFIGURATION
# =============================================================================

# Constraint weights organized by priority and type
constraints:
  # -------------------------------------------------------------------------
  # HARD CONSTRAINTS - Hospital-Level Safety & Standards
  # -------------------------------------------------------------------------
  hard:
    # Skills & Qualifications (CRITICAL - 3M: Hospital-level expertise required)
    c001_skills: # rename - skills_match
      weight: "hard:3000000"
      description: "Must have matching skills (RN, NP, PA, MD)"
      parameters:
        required_certifications: ["RN", "NP", "PA", "MD"] -- role
        specialty_skills: ["advanced_life_support", "iv_therapy", "ventilator_management", "cardiac_monitoring"] -- remove
        # TODO: Implemention Pending: skill_verification_required: true -- remove
        # TODO: Implemention Pending: license_expiry_check: true -- remove
        # TODO: Implemention Pending: quality_assurance_required: true --remove

    # Provider Availability (CRITICAL - 3M: 24/7 coverage model)
    c002_provider_availability:
    #max task points per dqy
      start_time_weight: "hard:3000000"
      end_time_weight: "hard:3000000"
      description: "24/7 coverage for hospital-level care"
      parameters:
        availability_buffer_minutes: 0           # 24/7 coverage model
        shift_overlap_required: true             # Continuous coverage
        on_call_coverage_enabled: true
        emergency_override_enabled: true

    # Team Coordination (HIGH - 1.5M: Multiple providers allowed)
    c003_overlap:
      weight: "hard:1500000"
      description: "Coordinated care team visits"
      parameters:
        minimum_gap_minutes: 10                  # Team coordination allowed
        allow_same_location_overlap: true        # Multiple providers for complex care
        care_team_coordination: true
        max_concurrent_providers: 3

    # Visit Completion (HIGH - 2M/1.5M: Hospital standards)
    c004_visit_completion:
      required_unassigned_weight: "hard:2000000"
      time_window_violation_weight: "hard:1500000"
      description: "All hospital@home visits must be completed"
      parameters:
        allow_partial_completion: false          # Hospital standards
        completion_buffer_minutes: 5
        quality_assurance_required: true
        hospital_level_standards: true

    # Travel Limits (MODERATE - 800K: Rapid response needed)
    c005_max_travel:
      distance_weight: "hard:800000"
      description: "Travel limits for emergency response"
      parameters:
        max_travel_distance_miles: 15            # Rapid response required
        max_travel_time_minutes: 30
        emergency_travel_multiplier: 1.5
        rapid_response_enabled: true
        traffic_buffer_multiplier: 1.5           # 50% buffer for emergency response
        equipment_setup_time_minutes: 15

    # Break Requirements (LOW - 300K: Emergency coverage priority)
    c006_breaks:
      weight: "hard:300000"
      description: "Break requirements with emergency override"
      parameters:
        min_break_minutes: 20                    # Flexible for emergency coverage
        max_continuous_hours: 6.0                # Intensive care demands
        lunch_break_required: true
        lunch_break_duration_minutes: 20
        emergency_override_enabled: true

  # -------------------------------------------------------------------------
  # SOFT CONSTRAINTS - Optimal Patient Experience & Operations
  # -------------------------------------------------------------------------
  soft:
    # Urgency & Priority (Critical - Hospital-level acuity)
    c007_urgency:
      weight: "soft:600"
      description: "Emergency and critical care prioritization"
      parameters:
        urgent_decay_hours: 1                    # Hospital-level acuity
        critical_multiplier: 10.0
        emergency_escalation_immediate: true
        priority_levels: ["routine", "urgent", "critical", "emergency", "code"]
        code_response_minutes: 5

    # Continuity of Care (Critical - Complex patient coordination)
    c008_continuity:
      weight: "soft:500"
      description: "Same provider for complex care episodes"
      parameters:
        same_provider_bonus: 100                 # Critical for complex patients
        provider_familiarity_weight: 60
        care_plan_continuity: true
        complex_care_tracking: true
        episode_based_assignment: true

    # Patient Preferences (High - Comfort during intensive care)
    c009_patient_preferences:
      weight: "soft:250"
      description: "Patient comfort during intensive care"
      parameters:
        preference_weight: 35                    # Patient comfort very important
        strict_preferences_enabled: true
        cultural_matching_weight: 25
        family_involvement_enabled: true
        comfort_priority_during_intensive_care: true

    # Travel Optimization (High - Emergency response times)
    c011_travel_time:
      weight: "soft:200"
      description: "Minimize travel time for rapid response"
      parameters:
        minimize_travel_enabled: true
        travel_efficiency_weight: 25             # Rapid response
        geographic_clustering_enabled: true
        emergency_route_optimization: true
        response_time_target_minutes: 30

    # Workload Balance (High - Intensive care management)
    c012_workload_balance:
      weight: "soft:180"
      description: "Balance intensive care workload"
      parameters:
        balance_weight: 35                       # Intensive care management
        max_workload_variance: 0.2
        consider_task_complexity: true
        intensive_care_rotation: true
        burnout_prevention_enabled: true

    # Provider Preferences (Moderate - Team dynamics)
    c010_provider_preferences:
      weight: "soft:120"
      description: "Provider preferences for team dynamics"
      parameters:
        provider_preference_weight: 25           # Specialized team preferences
        schedule_preference_weight: 20
        workload_preference_weight: 30
        team_dynamics_enabled: true
        specialized_team_coordination: true

# =============================================================================
# GLOBAL CONFIGURATION - Service-Level Settings
# =============================================================================

# Priority weights for visit assignment (used by urgency constraint)
priority_weights:
  CODE: 100000                                # Life-threatening emergencies
  EMERGENCY: 50000                            # Hospital-level emergencies
  CRITICAL: 5000                              # Critical intensive care
  URGENT: 500                                 # Urgent hospital-level care
  HIGH: 100                                   # High priority intensive care
  NORMAL: 20                                  # Standard hospital@home visits
  LOW: 1                                      # Routine monitoring

# Service-level operational settings
service_settings:
  healthcare_service: "hospital_at_home"
  regulatory_level: "critical"
  clinical_complexity: "intensive"

  # Risk management
  visit_completion_risk:
    minimal_time_to_shift_end_hours: 1        # 1 hour before shift end
    minimal_priority: "CRITICAL"              # Only critical+ visits considered

  # US market settings
  distance_units: "miles"                     # US market preference
  time_zone: "America/New_York"               # Default timezone

score_type: "hard_soft"
environment_mode: "REPRODUCIBLE"

# Solver configuration - comprehensive optimization for complex care
solver_config:
  time_limit_seconds: 180
  unimproved_seconds_spent_limit: 90
  best_score_limit: null
  
# Metadata for operational use
metadata:
  created_by: "system"
  created_date: "2024-01-01"
  last_modified: "2024-01-01"
  use_cases: ["hospital_at_home", "acute_care", "intensive_monitoring", "complex_medical"]
  target_services: ["24/7 monitoring", "IV therapy", "ventilator care", "complex wound care", "medication titration"]
  regulatory_requirements: ["hospital_standards", "cms_conditions", "state_licensing", "accreditation"]
  complexity_level: "hospital_equivalent"
  description: "Designed for hospital-level care delivered at home. Emphasizes rapid response, 24/7 coverage, coordinated care teams, and hospital-equivalent quality standards with intensive patient monitoring."
