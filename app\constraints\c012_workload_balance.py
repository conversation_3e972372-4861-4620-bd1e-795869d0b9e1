# app/constraints/c012_workload_balance.py
"""
c012_workload_balance: Workload balancing.
Priority: 12 - Balance daily workload by penalizing resource segments that stray far from the daily average.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c012_workload_balance_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "balance_tolerance": float  # fraction of average visits per day
        }
    """
    # Configuration parameters (currently unused but available for future enhancements)

    @constraint_provider
    def define_constraints(factory):
        # 1) Count tasks per resource
        # 2) Compute deviation from average (for all active resources, if you can query that)
        # For simplicity, here's a stub: penalize if resource.taskCount > average*(1+tolerance)
        return [
            factory.for_each(Task)
                   .filter(lambda t: False)  # Placeholder - always false for now
                   .penalize("WORKLOAD_BALANCE_OPTIMIZATION", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints
