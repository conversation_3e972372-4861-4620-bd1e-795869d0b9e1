"""
Assignment Solution Domain Model
===============================

Timefold solution class for healthcare assignment optimization.
This module defines the complete domain model required for Timefold solver integration.
"""

from typing import List, Optional, Annotated
from dataclasses import dataclass, field
from datetime import datetime

# Direct Timefold imports following standard pattern
from timefold.solver.domain import (
    planning_solution,
    planning_entity,
    PlanningVariable,
    PlanningScore,
    ValueRangeProvider,
    PlanningEntityCollectionProperty,
    ProblemFactCollectionProperty,
    PlanningId
)
from timefold.solver.score import HardSoftScore

from app.domain.models import Provider, Consumer, Location, Task


@planning_entity
@dataclass
class Assignment:
    """
    Planning entity representing a task assignment to a provider.

    This is the core planning entity that Timefold will optimize.
    Each assignment represents one task that needs to be assigned to a provider
    with a specific start time.
    """

    # Problem facts (immutable during planning)
    id: Annotated[str, PlanningId]
    consumer_id: str
    task_type: str
    required_skills: List[str]
    duration_min: int
    earliest_start: datetime
    latest_end: datetime
    priority: str
    location: Location

    # Task dependency fields
    sequence_group: Optional[str] = None
    sequence_order: Optional[int] = None
    prerequisite_task_ids: List[str] = field(default_factory=list)
    same_provider_required: bool = False
    dependency_type: str = "none"

    # Planning variables (will be optimized by Timefold)
    assigned_provider: Annotated[Optional[Provider], PlanningVariable(value_range_provider_refs=["provider_range"])] = None
    assigned_start_time: Annotated[Optional[datetime], PlanningVariable(value_range_provider_refs=["time_slot_range"])] = None
    
    # Derived properties
    properties: dict = field(default_factory=dict)
    
    @property
    def assigned_end_time(self) -> Optional[datetime]:
        """Calculate end time based on start time and duration."""
        if self.assigned_start_time is None:
            return None
        from datetime import timedelta
        return self.assigned_start_time + timedelta(minutes=self.duration_min)
    
    @property
    def is_assigned(self) -> bool:
        """Check if this assignment has been fully assigned."""
        return self.assigned_provider is not None and self.assigned_start_time is not None
    
    def __str__(self) -> str:
        provider_name = self.assigned_provider.name if self.assigned_provider else "Unassigned"
        start_time = self.assigned_start_time.strftime("%H:%M") if self.assigned_start_time else "No time"
        return f"{self.task_type} → {provider_name} @ {start_time}"


@planning_solution
@dataclass
class AssignmentSolution:
    """
    Timefold planning solution for healthcare assignment optimization.

    This class represents the complete problem instance including:
    - Problem facts (providers, consumers, time slots)
    - Planning entities (assignments to be optimized)
    - Planning score (optimization objective)
    """

    # Problem facts (immutable data)
    providers: Annotated[List[Provider], ProblemFactCollectionProperty, ValueRangeProvider(id="provider_range")] = field(default_factory=list)
    consumers: Annotated[List[Consumer], ProblemFactCollectionProperty] = field(default_factory=list)
    time_slots: Annotated[List[datetime], ProblemFactCollectionProperty, ValueRangeProvider(id="time_slot_range")] = field(default_factory=list)

    # Planning entities (to be optimized)
    assignments: Annotated[List[Assignment], PlanningEntityCollectionProperty] = field(default_factory=list)

    # Planning score (calculated by constraints)
    score: Annotated[Optional[HardSoftScore], PlanningScore] = None

    # Configuration and metadata
    tenant_id: str = ""
    optimization_config: dict = field(default_factory=dict)
    
    @property
    def total_assignments(self) -> int:
        """Total number of assignments in the solution."""
        return len(self.assignments)
    
    @property
    def assigned_count(self) -> int:
        """Number of fully assigned tasks."""
        return sum(1 for assignment in self.assignments if assignment.is_assigned)
    
    @property
    def unassigned_count(self) -> int:
        """Number of unassigned tasks."""
        return self.total_assignments - self.assigned_count
    
    @property
    def assignment_rate(self) -> float:
        """Percentage of tasks that are assigned."""
        if self.total_assignments == 0:
            return 0.0
        return (self.assigned_count / self.total_assignments) * 100
    
    def get_assignments_by_provider(self, provider: Provider) -> List[Assignment]:
        """Get all assignments for a specific provider."""
        return [
            assignment for assignment in self.assignments
            if assignment.assigned_provider == provider
        ]
    
    def get_assignments_by_consumer(self, consumer: Consumer) -> List[Assignment]:
        """Get all assignments for a specific consumer."""
        return [
            assignment for assignment in self.assignments
            if assignment.consumer_id == consumer.id
        ]
    
    def get_assignments_by_sequence_group(self, sequence_group: str) -> List[Assignment]:
        """Get all assignments in a specific sequence group."""
        assignments = [
            assignment for assignment in self.assignments
            if assignment.sequence_group == sequence_group
        ]
        # Sort by sequence order
        return sorted(assignments, key=lambda a: a.sequence_order or 0)
    
    def validate_solution(self) -> List[str]:
        """
        Validate the solution for basic consistency.
        Returns list of validation errors.
        """
        errors = []
        
        # Check for duplicate assignments
        assigned_slots = {}
        for assignment in self.assignments:
            if assignment.is_assigned and assignment.assigned_provider:
                key = (assignment.assigned_provider.id, assignment.assigned_start_time)
                if key in assigned_slots:
                    errors.append(f"Duplicate assignment: {assignment} conflicts with {assigned_slots[key]}")
                else:
                    assigned_slots[key] = assignment
        
        # Check time window constraints
        for assignment in self.assignments:
            if assignment.assigned_start_time:
                if assignment.assigned_start_time < assignment.earliest_start:
                    errors.append(f"Assignment {assignment} starts before earliest allowed time")
                if assignment.assigned_end_time and assignment.assigned_end_time > assignment.latest_end:
                    errors.append(f"Assignment {assignment} ends after latest allowed time")
        
        # Check skill requirements
        for assignment in self.assignments:
            if assignment.assigned_provider:
                missing_skills = set(assignment.required_skills) - set(assignment.assigned_provider.skills)
                if missing_skills:
                    errors.append(f"Assignment {assignment} missing skills: {missing_skills}")
        
        return errors
    
    def get_solution_summary(self) -> dict:
        """Get a summary of the solution state."""
        return {
            "total_assignments": self.total_assignments,
            "assigned_count": self.assigned_count,
            "unassigned_count": self.unassigned_count,
            "assignment_rate": f"{self.assignment_rate:.1f}%",
            "total_providers": len(self.providers),
            "total_consumers": len(self.consumers),
            "total_time_slots": len(self.time_slots),
            "score": str(self.score) if self.score else "Not calculated",
            "validation_errors": len(self.validate_solution())
        }
    
    def __str__(self) -> str:
        return f"AssignmentSolution({self.assigned_count}/{self.total_assignments} assigned, score={self.score})"


def create_time_slots(start_time: datetime, end_time: datetime, interval_minutes: int = 15) -> List[datetime]:
    """
    Create a list of time slots for planning variable range.
    
    Args:
        start_time: Start of the planning window
        end_time: End of the planning window  
        interval_minutes: Interval between time slots in minutes
        
    Returns:
        List of datetime objects representing available time slots
    """
    from datetime import timedelta
    
    time_slots = []
    current_time = start_time
    
    while current_time <= end_time:
        time_slots.append(current_time)
        current_time += timedelta(minutes=interval_minutes)
    
    return time_slots


def convert_tasks_to_assignments(tasks: List, consumers: List[Consumer]) -> List[Assignment]:
    """
    Convert Task domain objects to Assignment planning entities.
    
    Args:
        tasks: List of Task domain objects
        consumers: List of Consumer domain objects for reference
        
    Returns:
        List of Assignment planning entities ready for optimization
    """
    assignments = []
    
    for task in tasks:
        assignment = Assignment(
            id=str(task.id),
            consumer_id=str(task.consumer_id),
            task_type=task.task_type,
            required_skills=task.required_skills,
            duration_min=task.duration_min,
            earliest_start=task.earliest_start,
            latest_end=task.latest_end,
            priority=task.priority,
            location=task.location,
            sequence_group=task.sequence_group,
            sequence_order=task.sequence_order,
            prerequisite_task_ids=[str(pid) for pid in task.prerequisite_task_ids],
            same_provider_required=task.same_provider_required,
            dependency_type=task.dependency_type,
            properties=task.properties
        )
        assignments.append(assignment)
    
    return assignments
