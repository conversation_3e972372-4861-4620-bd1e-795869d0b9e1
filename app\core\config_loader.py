
"""
Configuration loader with hot reload capabilities.

This module handles loading and caching of tenant-specific YAML configuration
profiles with automatic hot reload functionality.
"""

import os
import yaml
import aiofiles
import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, Set
from datetime import datetime, timedelta
import threading
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)

# Configuration cache and metadata
_config_cache: Dict[str, Dict[str, Any]] = {}
_cache_timestamps: Dict[str, datetime] = {}
_cache_lock = asyncio.Lock()

# Configuration directory - can be overridden by environment
CONFIG_DIR = Path(os.getenv('CONFIG_DIR', '/configs'))
FALLBACK_CONFIG_DIR = Path('app/config_profiles')  # Local fallback for development

# Cache settings
CACHE_TTL_SECONDS = int(os.getenv('CONFIG_CACHE_TTL', '300'))  # 5 minutes default
HOT_RELOAD_ENABLED = os.getenv('HOT_RELOAD_ENABLED', 'true').lower() == 'true'
RELOAD_CHECK_INTERVAL = int(os.getenv('RELOAD_CHECK_INTERVAL', '30'))  # 30 seconds


class ConfigFileMonitor:
    """Simple file modification time monitor for configuration files."""

    def __init__(self, cache_invalidator):
        self.cache_invalidator = cache_invalidator
        self.file_mtimes: Dict[str, float] = {}
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None

    async def start_monitoring(self):
        """Start monitoring configuration files for changes."""
        if self._monitoring:
            return

        self._monitoring = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info(f"Started config file monitoring for: {CONFIG_DIR}")

    async def stop_monitoring(self):
        """Stop monitoring configuration files."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped config file monitoring")

    async def _monitor_loop(self):
        """Main monitoring loop that checks file modification times."""
        while self._monitoring:
            try:
                await self._check_file_changes()
                await asyncio.sleep(RELOAD_CHECK_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in config monitoring loop: {e}")
                await asyncio.sleep(RELOAD_CHECK_INTERVAL)

    async def _check_file_changes(self):
        """Check for configuration file changes."""
        if not CONFIG_DIR.exists():
            return

        for config_file in CONFIG_DIR.glob("*.yml"):
            try:
                current_mtime = config_file.stat().st_mtime
                tenant_id = config_file.stem

                if tenant_id in self.file_mtimes:
                    if current_mtime > self.file_mtimes[tenant_id]:
                        logger.info(f"Configuration file changed: {config_file}")
                        self.cache_invalidator(tenant_id)

                self.file_mtimes[tenant_id] = current_mtime

            except Exception as e:
                logger.warning(f"Error checking file {config_file}: {e}")


class ConfigLoader:
    """Enhanced configuration loader with hot reload capabilities."""

    def __init__(self):
        self.monitor: Optional[ConfigFileMonitor] = None
        self._shutdown_event = threading.Event()

    def invalidate_cache(self, tenant_id: str) -> None:
        """Invalidate cache for a specific tenant."""
        if tenant_id in _config_cache:
            del _config_cache[tenant_id]
            logger.info(f"Invalidated cache for tenant: {tenant_id}")

        if tenant_id in _cache_timestamps:
            del _cache_timestamps[tenant_id]

    async def start_file_watcher(self) -> None:
        """Start the file system watcher for hot reload."""
        if not HOT_RELOAD_ENABLED:
            logger.info("Hot reload is disabled")
            return

        if not CONFIG_DIR.exists():
            logger.warning(f"Config directory does not exist: {CONFIG_DIR}")
            return

        try:
            self.monitor = ConfigFileMonitor(self.invalidate_cache)
            await self.monitor.start_monitoring()
        except Exception as e:
            logger.error(f"Failed to start file monitoring: {e}")

    async def stop_file_watcher(self) -> None:
        """Stop the file system watcher."""
        if self.monitor:
            await self.monitor.stop_monitoring()
            logger.info("Stopped file monitoring")

    async def _load_config_file(self, config_path: Path) -> Dict[str, Any]:
        """Load configuration from a YAML file."""
        try:
            async with aiofiles.open(config_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                config = yaml.safe_load(content)

                if not isinstance(config, dict):
                    raise ValueError("Configuration must be a YAML dictionary")

                # Validate required fields
                required_fields = ['profile', 'constraint_set', 'weights']
                missing_fields = [field for field in required_fields if field not in config]
                if missing_fields:
                    raise ValueError(f"Missing required fields: {missing_fields}")

                logger.debug(f"Loaded configuration from: {config_path}")
                return config

        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in {config_path}: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration from {config_path}: {e}")

    async def _find_config_file(self, tenant_id: str) -> Path:
        """Find the configuration file for a tenant."""
        # Try primary config directory first
        primary_path = CONFIG_DIR / f"{tenant_id}.yml"
        if primary_path.exists():
            return primary_path

        # Try fallback directory (for development)
        fallback_path = FALLBACK_CONFIG_DIR / f"{tenant_id}.yml"
        if fallback_path.exists():
            logger.debug(f"Using fallback config for {tenant_id}: {fallback_path}")
            return fallback_path

        raise FileNotFoundError(f"No configuration found for tenant: {tenant_id}")

    def _is_cache_valid(self, tenant_id: str) -> bool:
        """Check if cached configuration is still valid."""
        if tenant_id not in _cache_timestamps:
            return False

        cache_time = _cache_timestamps[tenant_id]
        ttl_expired = datetime.now() - cache_time > timedelta(seconds=CACHE_TTL_SECONDS)

        return not ttl_expired

    async def load_profile(self, tenant_id: str, force_reload: bool = False) -> Dict[str, Any]:
        """
        Load configuration profile for a tenant.

        Args:
            tenant_id: The tenant identifier
            force_reload: Force reload from disk, bypassing cache

        Returns:
            Dictionary containing the tenant's configuration

        Raises:
            FileNotFoundError: If configuration file is not found
            ValueError: If configuration is invalid
            RuntimeError: If loading fails for other reasons
        """
        async with _cache_lock:
            # Check cache first (unless force reload)
            if not force_reload and tenant_id in _config_cache and self._is_cache_valid(tenant_id):
                logger.debug(f"Returning cached config for tenant: {tenant_id}")
                return _config_cache[tenant_id]

            # Load from file
            try:
                config_path = await self._find_config_file(tenant_id)
                config = await self._load_config_file(config_path)

                # Cache the configuration
                _config_cache[tenant_id] = config
                _cache_timestamps[tenant_id] = datetime.now()

                logger.info(f"Loaded configuration for tenant: {tenant_id}")
                return config

            except Exception as e:
                logger.error(f"Failed to load configuration for tenant {tenant_id}: {e}")
                raise

    async def get_all_tenant_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load configurations for all available tenants."""
        configs = {}

        # Scan config directories for YAML files
        config_files = []

        if CONFIG_DIR.exists():
            config_files.extend(CONFIG_DIR.glob("*.yml"))

        if FALLBACK_CONFIG_DIR.exists():
            config_files.extend(FALLBACK_CONFIG_DIR.glob("*.yml"))

        for config_file in config_files:
            tenant_id = config_file.stem
            try:
                configs[tenant_id] = await self.load_profile(tenant_id)
            except Exception as e:
                logger.error(f"Failed to load config for {tenant_id}: {e}")

        return configs

    def clear_cache(self, tenant_id: Optional[str] = None) -> None:
        """Clear configuration cache."""
        if tenant_id:
            self.invalidate_cache(tenant_id)
        else:
            _config_cache.clear()
            _cache_timestamps.clear()
            logger.info("Cleared all configuration cache")

    async def reload_all_configs(self) -> None:
        """Force reload all cached configurations."""
        tenant_ids = list(_config_cache.keys())
        for tenant_id in tenant_ids:
            try:
                await self.load_profile(tenant_id, force_reload=True)
            except Exception as e:
                logger.error(f"Failed to reload config for {tenant_id}: {e}")


# Global config loader instance
config_loader = ConfigLoader()


async def load_profile(tenant_id: str, force_reload: bool = False) -> Dict[str, Any]:
    """
    Load configuration profile for a tenant.

    This is the main function to use for loading tenant configurations.
    """
    return await config_loader.load_profile(tenant_id, force_reload)


async def reload_watcher(interval: int = RELOAD_CHECK_INTERVAL) -> None:
    """
    Background task for periodic cache cleanup and reload checking.

    This function runs as a background task and periodically:
    1. Cleans up expired cache entries
    2. Optionally reloads configurations
    """
    logger.info(f"Started reload watcher with {interval}s interval")

    while True:
        try:
            # Clean up expired cache entries
            now = datetime.now()
            expired_tenants = [
                tenant_id for tenant_id, timestamp in _cache_timestamps.items()
                if now - timestamp > timedelta(seconds=CACHE_TTL_SECONDS)
            ]

            for tenant_id in expired_tenants:
                config_loader.invalidate_cache(tenant_id)
                logger.debug(f"Expired cache for tenant: {tenant_id}")

            await asyncio.sleep(interval)

        except Exception as e:
            logger.error(f"Error in reload watcher: {e}")
            await asyncio.sleep(interval)


async def start_config_watcher() -> None:
    """Start the configuration file watcher."""
    await config_loader.start_file_watcher()


async def stop_config_watcher() -> None:
    """Stop the configuration file watcher."""
    await config_loader.stop_file_watcher()


# Convenience functions for backward compatibility
async def reload_loop(interval: int = 30) -> None:
    """Legacy function name - redirects to reload_watcher."""
    await reload_watcher(interval)
