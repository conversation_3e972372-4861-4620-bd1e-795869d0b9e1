"""
Task Dependencies Constraint (c014)
Enforces task prerequisites and dependencies.
"""

from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.solution import Assignment


def c014_task_dependencies_constraints(cfg: dict):
    """
    cfg: dict with optional parameters for task dependency configuration.
    """
    @constraint_provider
    def define_constraints(constraint_factory):
        """
        Enforce task prerequisites and dependencies.

        For now, this is a placeholder constraint that will be implemented
        when the full Timefold constraint API is available.
        """
        # Placeholder constraint - tasks with dependencies should be handled properly
        return [
            constraint_factory
                .for_each(Assignment)
                .filter(lambda assignment: len(assignment.prerequisite_task_ids) > 0)
                .filter(lambda assignment: assignment.assigned_provider is None)  # Unassigned dependent tasks
                .penalize("TASK_DEPENDENCY_UNASSIGNED", HardSoftScore.ONE_HARD)
        ]

    return define_constraints
