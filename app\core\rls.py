"""
Row Level Security (RLS) utilities for multi-tenant isolation.

This module provides utilities for managing PostgreSQL Row Level Security
to ensure tenant data isolation in a multi-tenant application.
"""

import logging
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from fastapi import HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

logger = logging.getLogger(__name__)

# Security scheme for tenant authentication
security = HTTPBearer(auto_error=False)


class TenantContext:
    """Context manager for tenant-specific operations."""
    
    def __init__(self):
        self._current_tenant: Optional[str] = None
    
    @property
    def current_tenant(self) -> Optional[str]:
        """Get the current tenant ID."""
        return self._current_tenant
    
    def set_tenant(self, tenant_id: str) -> None:
        """Set the current tenant ID."""
        self._current_tenant = tenant_id
    
    def clear_tenant(self) -> None:
        """Clear the current tenant ID."""
        self._current_tenant = None


# Global tenant context
tenant_context = TenantContext()


async def extract_tenant_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> str:
    """
    Extract tenant ID from JWT token or authorization header.
    
    In a real implementation, this would:
    1. Validate the JWT token
    2. Extract tenant_id from the token claims
    3. Verify tenant permissions
    
    For now, we'll use a simple approach where the tenant_id
    is passed in the Authorization header as "Bearer tenant_id"
    """
    if not credentials:
        raise HTTPException(
            status_code=401,
            detail="Authorization header required"
        )
    
    # In production, this would be a proper JWT validation
    tenant_id = credentials.credentials
    
    if not tenant_id or len(tenant_id) < 3:
        raise HTTPException(
            status_code=401,
            detail="Invalid tenant identifier"
        )
    
    # Set tenant context
    tenant_context.set_tenant(tenant_id)
    
    logger.debug(f"Extracted tenant ID: {tenant_id}")
    return tenant_id


async def extract_tenant_from_header(request: Request) -> str:
    """
    Alternative method to extract tenant from custom header.
    
    This can be used when tenant ID is passed via X-Tenant-ID header
    instead of Authorization header.
    """
    tenant_id = request.headers.get("X-Tenant-ID")
    
    if not tenant_id:
        raise HTTPException(
            status_code=400,
            detail="X-Tenant-ID header required"
        )
    
    if len(tenant_id) < 3:
        raise HTTPException(
            status_code=400,
            detail="Invalid tenant identifier"
        )
    
    # Set tenant context
    tenant_context.set_tenant(tenant_id)
    
    logger.debug(f"Extracted tenant ID from header: {tenant_id}")
    return tenant_id


async def set_rls_context(session: AsyncSession, tenant_id: str) -> None:
    """
    Set the RLS context for a database session.
    
    This sets the PostgreSQL session variable that RLS policies use
    to filter data by tenant.
    """
    try:
        await session.execute(
            text("SELECT set_config('app.current_tenant_id', :tenant_id, true)"),
            {"tenant_id": tenant_id}
        )
        logger.debug(f"Set RLS context for tenant: {tenant_id}")
    except Exception as e:
        logger.error(f"Failed to set RLS context for tenant {tenant_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to set tenant context"
        )


async def verify_rls_context(session: AsyncSession, expected_tenant: str) -> bool:
    """
    Verify that the RLS context is correctly set for the expected tenant.
    
    This is useful for debugging and ensuring RLS is working correctly.
    """
    try:
        result = await session.execute(
            text("SELECT current_setting('app.current_tenant_id', true)")
        )
        current_tenant = result.scalar()
        
        if current_tenant != expected_tenant:
            logger.warning(
                f"RLS context mismatch: expected {expected_tenant}, "
                f"got {current_tenant}"
            )
            return False
        
        return True
    except Exception as e:
        logger.error(f"Failed to verify RLS context: {e}")
        return False


@asynccontextmanager
async def tenant_session(session: AsyncSession, tenant_id: str):
    """
    Context manager that ensures RLS context is set for a session.
    
    Usage:
        async with tenant_session(session, tenant_id):
            # All queries in this block will be filtered by tenant
            result = await session.execute(select(Resource))
    """
    # Set RLS context
    await set_rls_context(session, tenant_id)
    
    try:
        yield session
    finally:
        # Optionally clear context (though session will be closed anyway)
        try:
            await session.execute(
                text("SELECT set_config('app.current_tenant_id', '', true)")
            )
        except Exception as e:
            logger.warning(f"Failed to clear RLS context: {e}")


class RLSMiddleware:
    """
    Middleware to automatically set tenant context for all requests.
    
    This middleware extracts the tenant ID from the request and sets
    the global tenant context, which can be used by database operations.
    """
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Extract tenant from request headers or path
            request = Request(scope, receive)
            
            # Try to extract tenant from various sources
            tenant_id = None
            
            # Method 1: From path parameter (e.g., /api/v1/tenants/{tenant_id}/...)
            path_parts = request.url.path.split('/')
            if len(path_parts) > 3 and path_parts[1] == 'api':
                for i, part in enumerate(path_parts):
                    if part == 'tenants' and i + 1 < len(path_parts):
                        tenant_id = path_parts[i + 1]
                        break
            
            # Method 2: From X-Tenant-ID header
            if not tenant_id:
                tenant_id = request.headers.get("X-Tenant-ID")
            
            # Method 3: From Authorization header (if it contains tenant info)
            if not tenant_id:
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    # In a real app, you'd decode JWT and extract tenant
                    tenant_id = auth_header[7:]  # Remove "Bearer "
            
            # Set tenant context if found
            if tenant_id:
                tenant_context.set_tenant(tenant_id)
                logger.debug(f"Middleware set tenant context: {tenant_id}")
        
        # Continue with the request
        await self.app(scope, receive, send)
        
        # Clear tenant context after request
        tenant_context.clear_tenant()


def get_current_tenant() -> str:
    """
    Get the current tenant ID from context.
    
    This can be used in dependency injection to get the tenant ID
    without having to extract it from the request again.
    """
    tenant_id = tenant_context.current_tenant
    if not tenant_id:
        raise HTTPException(
            status_code=400,
            detail="No tenant context available"
        )
    return tenant_id


async def validate_tenant_access(
    tenant_id: str,
    user_permissions: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Validate that the current user has access to the specified tenant.
    
    In a real implementation, this would check:
    1. User permissions/roles
    2. Tenant membership
    3. Access policies
    
    Args:
        tenant_id: The tenant ID to validate access for
        user_permissions: Optional user permissions/context
    
    Returns:
        True if access is allowed, False otherwise
    """
    # For now, we'll allow access if tenant_id is valid
    # In production, implement proper access control here
    
    if not tenant_id or len(tenant_id) < 3:
        return False
    
    # Add your tenant access validation logic here
    # Example checks:
    # - Is user a member of this tenant?
    # - Does user have required permissions?
    # - Is tenant active/enabled?
    
    logger.debug(f"Validated access for tenant: {tenant_id}")
    return True


# Dependency for FastAPI routes that require tenant context
async def require_tenant(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> str:
    """
    FastAPI dependency that ensures a valid tenant context.
    
    Usage in routes:
        @router.get("/data")
        async def get_data(tenant_id: str = Depends(require_tenant)):
            # tenant_id is guaranteed to be valid here
    """
    tenant_id = await extract_tenant_from_token(credentials)
    
    # Validate tenant access
    if not await validate_tenant_access(tenant_id):
        raise HTTPException(
            status_code=403,
            detail="Access denied for tenant"
        )
    
    return tenant_id
