#!/usr/bin/env python3
"""
Test script to verify constraint name changes work correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.core.config_loader import load_profile
from app.solver.factory import get_available_constraints

def test_constraint_names():
    """Test that all constraint names have been updated correctly."""
    print("Testing constraint name updates...")
    
    # Test config loading
    try:
        config = load_profile("test_task_dependencies")
        print(f"✓ Config loaded successfully with {len(config.constraint_set)} constraints")
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        return False
    
    # Test constraint discovery
    try:
        constraints = get_available_constraints()
        print(f"✓ Found {len(constraints)} available constraints")
        for name in sorted(constraints.keys()):
            print(f"  - {name}")
    except Exception as e:
        print(f"✗ Constraint discovery failed: {e}")
        return False
    
    print("\n✓ All constraint name updates successful!")
    return True

if __name__ == "__main__":
    success = test_constraint_names()
    sys.exit(0 if success else 1)
