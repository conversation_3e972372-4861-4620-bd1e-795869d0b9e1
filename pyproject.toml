[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "assignment-service"
version = "0.1.0"
description = "Multi-tenant assignment optimization service using Timefold-Py"
readme = "README.md"
authors = ["Assignment Service Team"]
license = "MIT"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.10"

fastapi = ">=0.115.0,<0.116.0"
uvicorn = ">=0.32.0,<0.33.0"
asyncpg = ">=0.29.0,<0.30.0"
sqlalchemy = ">=2.0.29,<2.1.0"
timefold = ">=1.22.0b,<2.0.0"
pydantic = ">=2.9.0,<3.0.0"
python-multipart = ">=0.0.12,<0.1.0"
aiofiles = ">=24.1.0,<25.0.0"
PyYAML = ">=6.0.1,<7.0.0"
alembic = ">=1.13.1,<2.0.0"
psycopg2-binary = ">=2.9.9,<3.0.0"
python-dotenv = ">=1.0.0,<2.0.0"
prometheus-client = ">=0.21.0,<1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "8.1.1"
pytest-asyncio = "0.23.6"
pytest-cov = "5.0.0"
black = "24.3.0"
isort = "5.13.2"
flake8 = "7.0.0"
mypy = "1.9.0"
pre-commit = "3.7.0"
httpx = "0.27.0"


[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | coreframework
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
skip_glob = ["coreframework/*"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
exclude = [
    "coreframework/",
    "build/",
    "dist/",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "-ra",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "coreframework/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
