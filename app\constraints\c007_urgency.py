# app/constraints/c007_urgency.py
"""
c007_urgency: Prioritize urgent visits.
Priority: 7 - Reward if urgent tasks are scheduled as early as possible.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c007_urgency_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "urgent_decay_hours": int   # For each hour after earliest_start, penalty doubles
        }
    """
    # Configuration parameters (currently unused but available for future enhancements)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .filter(lambda t: t.urgent)
                   .penalize("URGENT_TASK_PRIORITY", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints
