# app/constraints/c008_continuity.py
"""
c008_continuity: Continuity of care.
Priority: 8 - Prefer assigning the same provider to the same patient over time.
"""
from timefold.solver.score import HardSoftScore, constraint_provider
from app.domain.models import Task

def c008_continuity_constraints(cfg: dict):
    """
    Args:
        cfg: {
            "continuity_bonus": int  # Bonus for same provider to same patient
        }
    """
    # Configuration parameters (currently unused but available for future enhancements)

    @constraint_provider
    def define_constraints(factory):
        return [
            factory.for_each(Task)
                   .join(Task, lambda t1, t2: (
                       t1.id != t2.id and
                       t1.consumer_id == t2.consumer_id and  # Same patient
                       t1.assigned_provider != t2.assigned_provider and  # Different providers
                       t1.assigned_provider is not None and
                       t2.assigned_provider is not None
                   ))
                   .penalize("CARE_CONTINUITY_BROKEN", HardSoftScore.ONE_SOFT)
        ]
    return define_constraints
