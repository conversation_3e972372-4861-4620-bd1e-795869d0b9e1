#!/usr/bin/env python3
"""
Script to update constraint file headers and function names after renaming.
"""

import os
import re

# Mapping of old to new names
CONSTRAINT_RENAMES = {
    'c001_skill_match.py': {
        'old_header': '# app/constraints/c001_skills.py',
        'new_header': '# app/constraints/c001_skill_match.py',
        'old_docstring': 'c001_skills:',
        'new_docstring': 'c001_skill_match:',
        'old_function': 'def c001_skills_constraints(',
        'new_function': 'def c001_skill_match_constraints('
    },
    'c003_task_overlap.py': {
        'old_header': '# app/constraints/c003_overlap.py',
        'new_header': '# app/constraints/c003_task_overlap.py',
        'old_docstring': 'c003_overlap:',
        'new_docstring': 'c003_task_overlap:',
        'old_function': 'def c003_overlap_constraints(',
        'new_function': 'def c003_task_overlap_constraints('
    },
    'c004_task_completion.py': {
        'old_header': '# app/constraints/c004_visit_completion.py',
        'new_header': '# app/constraints/c004_task_completion.py',
        'old_docstring': 'c004_visit_completion:',
        'new_docstring': 'c004_task_completion:',
        'old_function': 'def c004_visit_completion_constraints(',
        'new_function': 'def c004_task_completion_constraints('
    },
    'c005_travel_distance.py': {
        'old_header': '# app/constraints/c005_max_travel.py',
        'new_header': '# app/constraints/c005_travel_distance.py',
        'old_docstring': 'c005_max_travel:',
        'new_docstring': 'c005_travel_distance:',
        'old_function': 'def c005_max_travel_constraints(',
        'new_function': 'def c005_travel_distance_constraints('
    },
    'c006_break_time.py': {
        'old_header': '# app/constraints/c006_breaks.py',
        'new_header': '# app/constraints/c006_break_time.py',
        'old_docstring': 'c006_breaks:',
        'new_docstring': 'c006_break_time:',
        'old_function': 'def c006_breaks_constraints(',
        'new_function': 'def c006_break_time_constraints('
    },
    'c007_urgent_priority.py': {
        'old_header': '# app/constraints/c007_urgency.py',
        'new_header': '# app/constraints/c007_urgent_priority.py',
        'old_docstring': 'c007_urgency:',
        'new_docstring': 'c007_urgent_priority:',
        'old_function': 'def c007_urgency_constraints(',
        'new_function': 'def c007_urgent_priority_constraints('
    },
    'c008_care_continuity.py': {
        'old_header': '# app/constraints/c008_continuity.py',
        'new_header': '# app/constraints/c008_care_continuity.py',
        'old_docstring': 'c008_continuity:',
        'new_docstring': 'c008_care_continuity:',
        'old_function': 'def c008_continuity_constraints(',
        'new_function': 'def c008_care_continuity_constraints('
    },
    'c009_patient_preference.py': {
        'old_header': '# app/constraints/c009_patient_pref.py',
        'new_header': '# app/constraints/c009_patient_preference.py',
        'old_docstring': 'c009_patient_pref:',
        'new_docstring': 'c009_patient_preference:',
        'old_function': 'def c009_patient_pref_constraints(',
        'new_function': 'def c009_patient_preference_constraints('
    },
    'c010_provider_preference.py': {
        'old_header': '# app/constraints/c010_provider_pref.py',
        'new_header': '# app/constraints/c010_provider_preference.py',
        'old_docstring': 'c010_provider_pref:',
        'new_docstring': 'c010_provider_preference:',
        'old_function': 'def c010_provider_pref_constraints(',
        'new_function': 'def c010_provider_preference_constraints('
    },
    'c011_travel_optimization.py': {
        'old_header': '# app/constraints/c011_travel_time.py',
        'new_header': '# app/constraints/c011_travel_optimization.py',
        'old_docstring': 'c011_travel_time:',
        'new_docstring': 'c011_travel_optimization:',
        'old_function': 'def c011_travel_time_constraints(',
        'new_function': 'def c011_travel_optimization_constraints('
    },
    'c012_workload_optimization.py': {
        'old_header': '# app/constraints/c012_workload_balance.py',
        'new_header': '# app/constraints/c012_workload_optimization.py',
        'old_docstring': 'c012_workload_balance:',
        'new_docstring': 'c012_workload_optimization:',
        'old_function': 'def c012_workload_balance_constraints(',
        'new_function': 'def c012_workload_optimization_constraints('
    },
    'c013_sequence_order.py': {
        'old_header': '# app/constraints/c013_task_sequence.py',
        'new_header': '# app/constraints/c013_sequence_order.py',
        'old_docstring': 'c013_task_sequence:',
        'new_docstring': 'c013_sequence_order:',
        'old_function': 'def c013_task_sequence_constraints(',
        'new_function': 'def c013_sequence_order_constraints('
    }
}

def update_constraint_file(filepath, updates):
    """Update a single constraint file."""
    print(f"Updating {filepath}...")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Apply updates
    for old, new in updates.items():
        if old.startswith('old_'):
            new_key = old.replace('old_', 'new_')
            if new_key in updates:
                content = content.replace(updates[old], updates[new_key])
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ Updated {filepath}")

def main():
    """Update all constraint files."""
    constraints_dir = "d:/Work/Assignment/assignment-service/app/constraints"
    
    for filename, updates in CONSTRAINT_RENAMES.items():
        filepath = os.path.join(constraints_dir, filename)
        if os.path.exists(filepath):
            update_constraint_file(filepath, updates)
        else:
            print(f"⚠️ File not found: {filepath}")
    
    print("✓ All constraint files updated!")

if __name__ == "__main__":
    main()
