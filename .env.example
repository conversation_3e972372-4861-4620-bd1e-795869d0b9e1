# Assignment Service Environment Configuration
# Copy this file to .env and update the values for your environment

# Database Configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/assignments

# Enable SQL query logging (true/false)
DB_ECHO=false

# Configuration Management
CONFIG_DIR=/configs
CONFIG_CACHE_TTL=300
HOT_RELOAD_ENABLED=true
RELOAD_CHECK_INTERVAL=30

# Application Settings
LOG_LEVEL=INFO
DEBUG=false

# Security (for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Solver Configuration
DEFAULT_SOLVE_TIME_LIMIT=60
MAX_SOLVE_TIME_LIMIT=300

# Performance Tuning
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_RECYCLE=3600

# Monitoring & Observability
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_TIMEOUT=30

# Development Settings (remove in production)
USE_MOCK_DATA=false
ENABLE_DEBUG_ENDPOINTS=false
