# Surgical Follow-Up Scenario

## Overview
This scenario tests post-surgical care coordination requiring specific provider continuity and sequential assessments.

## Healthcare Workflow
**Post-Surgical Care Episode**: A patient requires comprehensive follow-up care after surgery with specific provider requirements and timing constraints.

### Task Sequence
1. **Surgeon Assessment** (45 min)
   - Surgical site examination
   - Healing progress evaluation
   - Complication screening
   - Required skills: `surgical_assessment`, `wound_evaluation`

2. **Wound Care Specialist** (30 min)
   - Detailed wound assessment
   - Dressing change if needed
   - Healing protocol adjustment
   - Required skills: `wound_care`, `infection_control`
   - **Dependency**: Must occur after surgeon assessment
   - **Same Provider**: Preferred for continuity

3. **Patient Education** (20 min)
   - Recovery instructions
   - Activity restrictions
   - Warning signs education
   - Required skills: `patient_education`, `recovery_planning`
   - **Dependency**: Must occur after wound care
   - **Same Provider**: Can be any qualified educator

## Test Objectives
- **Provider Continuity**: Test same-provider preferences for related tasks
- **Clinical Sequencing**: Ensure proper medical assessment order
- **Skill Specialization**: Match highly specialized skills to appropriate providers
- **Recovery Optimization**: Optimize scheduling for patient recovery needs

## Expected Outcomes
- <PERSON><PERSON> performs initial assessment
- Wound care specialist provides specialized care (preferably same provider)
- Patient education completed by qualified educator
- Proper timing between assessments
- Continuity of care maintained where beneficial

## Configuration
- **Profile**: `surgical_follow_up_constraints`
- **Constraint Focus**: Provider continuity, clinical sequencing, specialization
- **Priority**: Quality outcomes through proper post-surgical care sequence
