# =============================================================================
# TASKS - Task Dependencies Scenario
# =============================================================================
# Sequential wound care tasks with dependencies and provider requirements
# Scenario: 4 tasks - Tasks 1-3 must be same RN in sequence, Task 4 any provider

tasks:
  # -------------------------------------------------------------------------
  # TASK 1: Wound Assessment (First in sequence, RN required)
  # -------------------------------------------------------------------------
  - id: "770e8400-e29b-41d4-a716-446655440001"
    consumer_id: "660e8400-e29b-41d4-a716-446655440001"
    
    task_details:
      task_type: "wound_assessment"
      description: "Comprehensive diabetic foot ulcer assessment"
      duration_minutes: 30
      complexity: "moderate"
    
    scheduling:
      earliest_start: "2024-01-15T09:00:00"
      latest_end: "2024-01-15T17:00:00"
      preferred_time_windows:
        - start: "09:00"
          end: "11:00"
        - start: "13:00" 
          end: "15:00"
    
    requirements:
      required_skills: ["wound_care", "assessment"]
      required_certifications: ["RN"]
      priority: "high"
      urgency_level: "routine"
      
    location:
      lat: 40.7282
      lng: -73.7949
      address: "123 Main St, Queens, NY 11375"
    
    # Task Dependencies Configuration
    dependencies:
      sequence_group: "wound_care_episode_001"
      sequence_order: 1
      prerequisite_task_ids: []
      same_provider_required: true
      dependency_type: "sequence"
    
    clinical_context:
      visit_type: "wound_care"
      assessment_type: "comprehensive_initial"
      documentation_required: true
      
    properties:
      wound_location: "left_foot_plantar"
      assessment_components:
        - "wound_measurement"
        - "drainage_assessment" 
        - "surrounding_skin_evaluation"
        - "pain_assessment"
        - "circulation_check"
        - "sensation_testing"

  # -------------------------------------------------------------------------
  # TASK 2: Wound Cleaning (Second in sequence, same RN required)
  # -------------------------------------------------------------------------
  - id: "770e8400-e29b-41d4-a716-446655440002"
    consumer_id: "660e8400-e29b-41d4-a716-446655440001"
    
    task_details:
      task_type: "wound_cleaning"
      description: "Diabetic foot ulcer cleaning and debridement"
      duration_minutes: 45
      complexity: "moderate_high"
    
    scheduling:
      earliest_start: "2024-01-15T09:00:00"
      latest_end: "2024-01-15T17:00:00"
      preferred_time_windows:
        - start: "09:30"
          end: "12:00"
        - start: "13:30"
          end: "16:00"
    
    requirements:
      required_skills: ["wound_care"]
      required_certifications: ["RN"]
      priority: "high"
      urgency_level: "routine"
      
    location:
      lat: 40.7282
      lng: -73.7949
      address: "123 Main St, Queens, NY 11375"
    
    # Task Dependencies Configuration
    dependencies:
      sequence_group: "wound_care_episode_001"
      sequence_order: 2
      prerequisite_task_ids: ["770e8400-e29b-41d4-a716-446655440001"]
      same_provider_required: true
      dependency_type: "sequence"
    
    clinical_context:
      visit_type: "wound_care"
      procedure_type: "cleaning_debridement"
      documentation_required: true
      
    properties:
      cleaning_protocol:
        solution: "normal_saline"
        technique: "gentle_irrigation"
        debridement_type: "conservative_sharp"
      supplies_needed:
        - "sterile_saline"
        - "gauze_pads"
        - "irrigation_syringe"
        - "forceps"

  # -------------------------------------------------------------------------
  # TASK 3: Wound Dressing (Third in sequence, same RN required)
  # -------------------------------------------------------------------------
  - id: "770e8400-e29b-41d4-a716-446655440003"
    consumer_id: "660e8400-e29b-41d4-a716-446655440001"
    
    task_details:
      task_type: "wound_dressing"
      description: "Apply appropriate dressing for diabetic foot ulcer"
      duration_minutes: 30
      complexity: "moderate"
    
    scheduling:
      earliest_start: "2024-01-15T09:00:00"
      latest_end: "2024-01-15T17:00:00"
      preferred_time_windows:
        - start: "10:15"
          end: "13:00"
        - start: "14:15"
          end: "16:30"
    
    requirements:
      required_skills: ["wound_care"]
      required_certifications: ["RN"]
      priority: "high"
      urgency_level: "routine"
      
    location:
      lat: 40.7282
      lng: -73.7949
      address: "123 Main St, Queens, NY 11375"
    
    # Task Dependencies Configuration
    dependencies:
      sequence_group: "wound_care_episode_001"
      sequence_order: 3
      prerequisite_task_ids: ["770e8400-e29b-41d4-a716-446655440002"]
      same_provider_required: true
      dependency_type: "sequence"
    
    clinical_context:
      visit_type: "wound_care"
      procedure_type: "dressing_application"
      documentation_required: true
      
    properties:
      dressing_protocol:
        primary_dressing: "hydrocolloid"
        secondary_dressing: "gauze_wrap"
        frequency: "daily_change"
        monitoring_instructions: "assess_daily"

  # -------------------------------------------------------------------------
  # TASK 4: Documentation (After all prerequisites, any qualified provider)
  # -------------------------------------------------------------------------
  - id: "770e8400-e29b-41d4-a716-446655440004"
    consumer_id: "660e8400-e29b-41d4-a716-446655440001"
    
    task_details:
      task_type: "documentation"
      description: "Complete wound care documentation and care plan update"
      duration_minutes: 15
      complexity: "low"
    
    scheduling:
      earliest_start: "2024-01-15T09:00:00"
      latest_end: "2024-01-15T17:00:00"
      preferred_time_windows:
        - start: "11:00"
          end: "17:00"
    
    requirements:
      required_skills: ["documentation"]
      required_certifications: []  # Any qualified provider
      priority: "normal"
      urgency_level: "routine"
      
    location:
      lat: 40.7282
      lng: -73.7949
      address: "123 Main St, Queens, NY 11375"
    
    # Task Dependencies Configuration
    dependencies:
      sequence_group: null  # Not part of the RN sequence
      sequence_order: null
      prerequisite_task_ids: 
        - "770e8400-e29b-41d4-a716-446655440001"  # Must wait for assessment
        - "770e8400-e29b-41d4-a716-446655440002"  # Must wait for cleaning
        - "770e8400-e29b-41d4-a716-446655440003"  # Must wait for dressing
      same_provider_required: false  # Can be any provider
      dependency_type: "completion"
    
    clinical_context:
      visit_type: "documentation"
      documentation_type: "wound_care_summary"
      care_plan_update_required: true
      
    properties:
      documentation_requirements:
        - "wound_assessment_summary"
        - "treatment_provided"
        - "patient_response"
        - "care_plan_updates"
        - "next_visit_planning"
        - "patient_education_provided"
      photo_documentation: true
      family_communication: true
