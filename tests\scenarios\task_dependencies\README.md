# Task Dependencies Scenario

## Overview

This scenario tests the assignment service's ability to handle **complex task dependencies and sequencing** in healthcare workflows. It validates that our constraint system can properly manage sequential care episodes where some tasks must be performed by the same provider in a specific order, while other tasks can be handled by any qualified provider after prerequisites are met.

## Business Case

**Diabetic Foot Ulcer Wound Care Episode**

A 68-year-old diabetic patient (<PERSON>) requires a comprehensive wound care episode for a foot ulcer. The care must follow a specific clinical sequence:

1. **Wound Assessment** (RN required) - 30 minutes
2. **Wound Cleaning** (RN required) - 45 minutes  
3. **Wound Dressing** (RN required) - 30 minutes
4. **Documentation & Follow-up** (any qualified provider) - 15 minutes

**Clinical Requirements:**
- Tasks 1-3 must be performed by the same RN to ensure continuity
- Tasks 1-3 must execute in sequence (assessment → cleaning → dressing)
- Task 4 can only start after tasks 1-3 are complete
- Task 4 can be performed by any provider with documentation skills
- Minimum 15-minute buffer between tasks for setup/travel

## Test Structure

```
tests/scenarios/task_dependencies/
├── input/
│   ├── providers.yml      # 4 healthcare providers (2 RNs, 1 LPN, 1 CNA)
│   ├── consumers.yml      # 1 patient with diabetic foot ulcer
│   └── tasks.yml          # 4 sequential wound care tasks
├── expected_output/
│   └── optimized_schedule.yml   # Expected optimal assignment
├── test_task_dependencies.py   # Scenario test implementation
└── README.md             # This file
```

## Input Data

### Providers (`input/providers.yml`)
- **Alice Johnson, RN**: Wound care specialist, 8 years experience
- **Bob Smith, RN**: Wound care certified, 5 years experience  
- **Carol Davis, CNA**: Documentation specialist, 3 years experience
- **Diana Martinez, LPN**: Basic wound care, 4 years experience

### Consumer (`input/consumers.yml`)
- **John Doe**: 68-year-old male with diabetic foot ulcer
- **Location**: Queens, NY
- **Conditions**: Diabetes Type 2, diabetic foot ulcer, hypertension
- **Care Episode**: wound_care_episode_001

### Tasks (`input/tasks.yml`)
1. **Wound Assessment** (sequence_order: 1, same_provider_required: true)
2. **Wound Cleaning** (sequence_order: 2, same_provider_required: true)
3. **Wound Dressing** (sequence_order: 3, same_provider_required: true)
4. **Documentation** (prerequisite_task_ids: [1,2,3], same_provider_required: false)

## Expected Output

### Optimal Assignment (`expected_output/optimized_schedule.yml`)

**Schedule:**
- **09:00-09:30**: Task 1 (Assessment) → Alice Johnson, RN
- **09:45-10:30**: Task 2 (Cleaning) → Alice Johnson, RN  
- **10:45-11:15**: Task 3 (Dressing) → Alice Johnson, RN
- **11:30-11:45**: Task 4 (Documentation) → Carol Davis, CNA

**Why This Is Optimal:**
- ✅ All task dependencies satisfied
- ✅ Same RN (Alice) handles all clinical tasks for continuity
- ✅ Proper 15-minute buffers between tasks
- ✅ Efficient use of RN time (Alice) for clinical work
- ✅ Cost-effective delegation of documentation to CNA (Carol)
- ✅ Minimal travel time (all tasks at same location)

## Constraint Validation

The test validates these critical constraints:

1. **`tasks_1_2_3_same_provider`**: Tasks 1, 2, 3 assigned to same RN
2. **`tasks_1_2_3_sequential_order`**: Tasks execute in proper sequence with timing
3. **`task_4_after_all_prerequisites`**: Task 4 waits for 1-3 completion
4. **`task_4_any_provider_allowed`**: Task 4 can be assigned to any qualified provider
5. **`buffer_time_between_tasks`**: Minimum 15-minute gaps maintained

## Test Implementation

### Key Test Methods

- **`test_scenario_data_loaded_correctly()`**: Validates input data parsing
- **`test_constraint_validation_requirements()`**: Checks constraint definitions
- **`test_optimal_assignment_structure()`**: Validates timing and sequencing
- **`test_same_provider_requirement()`**: Ensures tasks 1-3 use same RN
- **`test_provider_skill_requirements()`**: Validates skill matching
- **`test_schedule_optimization_metrics()`**: Checks performance metrics
- **`test_scenario_business_logic()`**: Validates clinical workflow logic

### Running the Test

```bash
# Run the specific scenario
python -m pytest tests/scenarios/task_dependencies/test_task_dependencies.py -v

# Run with detailed output
python tests/scenarios/task_dependencies/test_task_dependencies.py
```

## Integration with Core System

This scenario exercises these core assignment service components:

### Constraint Providers
- **`c013_task_sequence`**: Enforces sequential task ordering
- **`c014_task_dependencies`**: Manages prerequisite relationships
- **`c001_skills`**: Validates provider skill requirements
- **`c008_continuity`**: Promotes same provider for related tasks

### Domain Models
- **Task Dependencies**: `sequence_group`, `sequence_order`, `prerequisite_task_ids`
- **Provider Requirements**: `same_provider_required`, `dependency_type`
- **Scheduling**: `earliest_start`, `latest_end`, buffer time management

### Configuration
- **Skilled Nursing Profile**: Uses high weights for dependency constraints
- **Hard Constraints**: 2M+ weights for critical sequencing requirements
- **Soft Constraints**: 300-500 weights for continuity optimization

## Extending This Scenario

To create additional dependency scenarios:

1. **Copy the folder structure**
2. **Modify input data** for new clinical workflows
3. **Update expected output** with new optimal assignments
4. **Adapt test methods** for scenario-specific validations
5. **Document business rationale** in README

### Example Extensions
- **Medication Administration**: Sequential tasks requiring pharmacist → nurse → documentation
- **Surgical Follow-up**: Surgeon assessment → wound care → patient education
- **Therapy Sessions**: Evaluation → treatment → progress documentation

## Success Criteria

This scenario passes when:
- ✅ All input data loads correctly from YAML files
- ✅ Task dependencies are properly enforced
- ✅ Sequential ordering is maintained with proper timing
- ✅ Same provider requirements are satisfied
- ✅ Cross-provider dependencies work correctly
- ✅ Expected output matches actual optimization results
- ✅ Business logic represents realistic healthcare workflow

This scenario demonstrates that our assignment service can handle complex, real-world healthcare scheduling requirements with multiple interdependent tasks and provider constraints.
