#!/usr/bin/env python3
"""
Test script to verify constraint file renaming works correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.solver.factory import get_provider_registry, get_available_constraints

def test_renamed_constraints():
    """Test that all renamed constraint files are discovered correctly."""
    print("Testing renamed constraint files...")
    
    # Test constraint discovery
    try:
        providers = get_provider_registry()
        print(f"✓ Found {len(providers)} constraint providers:")
        for name in sorted(providers.keys()):
            print(f"  - {name} → {providers[name]}")
    except Exception as e:
        print(f"✗ Provider registry failed: {e}")
        return False
    
    # Test constraint loading
    try:
        constraints = get_available_constraints()
        print(f"\n✓ Loaded {len(constraints)} constraint functions:")
        for name in sorted(constraints.keys()):
            print(f"  - {name}")
    except Exception as e:
        print(f"✗ Constraint loading failed: {e}")
        return False
    
    # Verify expected constraints are present
    expected_constraints = [
        'c001_skill_match',
        'c002_provider_availability', 
        'c003_task_overlap',
        'c004_task_completion',
        'c005_travel_distance',
        'c006_break_time',
        'c007_urgent_priority',
        'c008_care_continuity',
        'c009_patient_preference',
        'c010_provider_preference',
        'c011_travel_optimization',
        'c012_workload_optimization',
        'c013_sequence_order',
        'c014_task_dependencies'
    ]
    
    missing = set(expected_constraints) - set(providers.keys())
    if missing:
        print(f"✗ Missing constraints: {missing}")
        return False
    
    print("\n✓ All constraint file renames successful!")
    return True

if __name__ == "__main__":
    success = test_renamed_constraints()
    sys.exit(0 if success else 1)
