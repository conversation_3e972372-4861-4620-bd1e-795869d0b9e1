# =============================================================================
# SKILLED NURSING CONFIGURATION
# =============================================================================
# Healthcare Service: Skilled nursing with strict clinical requirements
# Focus: Regulatory compliance, patient safety, clinical excellence
# =============================================================================

# Profile metadata
profile:
  name: "skilled_nursing_constraints"
  description: "Skilled nursing services with strict clinical requirements and regulatory compliance"
  healthcare_service: "skilled_nursing"
  regulatory_level: "high"
  clinical_complexity: "advanced"

# =============================================================================
# CONSTRAINT CONFIGURATION - Optimized for Skilled Nursing
# =============================================================================

# Constraint weights organized by priority and type
constraints:
  # -------------------------------------------------------------------------
  # HARD CONSTRAINTS - Safety & Regulatory Requirements
  # -------------------------------------------------------------------------
  hard:
    # Skills & Qualifications (CRITICAL - 2M: Licensed professionals required)
    c001_skills:
      weight: "hard:2000000"
      description: "Must have proper license/certification (RN, LPN, wound care, medication management)"
      parameters:
        required_certifications: ["RN", "LPN", "CNA"]
        specialty_skills: ["wound_care", "medication_management", "iv_therapy"]
        skill_verification_required: true
        license_expiry_check: true
        certification_expiry_check: true

    # Provider Availability (CRITICAL - 2M: Licensed provider availability)
    c002_provider_availability:
      start_time_weight: "hard:2000000"
      end_time_weight: "hard:2000000"
      description: "Licensed provider must be available during task times"
      parameters:
        availability_buffer_minutes: 0          # Strict licensing requirements
        shift_overlap_allowed: false
        availability_check_enabled: true
        regulatory_compliance_required: true

    # Safety & Overlap Prevention (CRITICAL - 2M: Patient safety)
    c003_overlap:
      weight: "hard:2000000"
      description: "No double-booking of nurses for patient safety"
      parameters:
        minimum_gap_minutes: 15                 # Critical for patient safety
        allow_same_location_overlap: false
        emergency_override_enabled: false
        patient_safety_priority: true

    # Visit Completion (HIGH - 1.5M/1M: Medical necessity)
    c004_visit_completion:
      required_unassigned_weight: "hard:1500000"
      time_window_violation_weight: "hard:1000000"
      description: "All skilled visits must be completed (medical necessity)"
      parameters:
        allow_partial_completion: false         # All skilled visits required
        completion_buffer_minutes: 10
        required_visit_priority: "high"
        medical_necessity_enforcement: true

    # Break Requirements (MODERATE - 600K: Regulatory compliance)
    c006_breaks:
      weight: "hard:600000"
      description: "Regulatory break requirements for nurse safety"
      parameters:
        min_break_minutes: 30                   # Strict regulatory compliance
        max_continuous_hours: 4.0
        lunch_break_required: true
        lunch_break_duration_minutes: 30
        emergency_break_override: true

    # Travel Limits (LOW - 400K: Reasonable for skilled staff)
    c005_max_travel:
      distance_weight: "hard:400000"
      description: "Reasonable travel limits for skilled staff"
      parameters:
        max_travel_distance_miles: 35           # Skilled nurses cover larger territories
        max_travel_time_minutes: 60
        emergency_travel_multiplier: 3.0
        route_optimization_enabled: true

  # -------------------------------------------------------------------------
  # SOFT CONSTRAINTS - Quality & Optimization
  # -------------------------------------------------------------------------
  soft:
    # Urgency & Priority (High - Medical emergencies critical)
    c007_urgency:
      weight: "soft:400"
      description: "Medical emergencies take priority"
      parameters:
        urgent_decay_hours: 2                   # Medical emergencies are critical
        urgent_priority_weight: 200
        critical_urgent_multiplier: 5.0
        emergency_escalation_enabled: true
        priority_levels: ["routine", "urgent", "critical", "emergency"]

    # Continuity of Care (High - Same nurse for complex treatments)
    c008_continuity:
      weight: "soft:300"
      description: "Same nurse for ongoing treatments and complex care"
      parameters:
        same_provider_bonus: 75                 # Important for complex treatments
        provider_familiarity_weight: 40
        care_episode_tracking: true
        complex_treatment_continuity: true
        clinical_relationship_priority: true

    # Travel Optimization (Moderate - Efficiency important)
    c011_travel_time:
      weight: "soft:120"
      description: "Travel optimization for efficiency"
      parameters:
        minimize_travel_enabled: true
        travel_efficiency_weight: 15           # Efficiency important
        geographic_clustering_enabled: true
        route_optimization_level: "moderate"
        cost_per_mile_consideration: true

    # Workload Balance (Moderate - Prevent nurse burnout)
    c012_workload_balance:
      weight: "soft:100"
      description: "Workload balancing to prevent nurse burnout"
      parameters:
        balance_weight: 25                      # Prevent nurse burnout
        max_workload_variance: 0.25
        consider_task_complexity: true
        burnout_prevention_enabled: true
        nurse_retention_priority: true

    # Patient Preferences (Low-Moderate - Clinical needs first)
    c009_patient_preferences:
      weight: "soft:100"
      description: "Patient comfort with skilled procedures"
      parameters:
        preference_weight: 20                   # Clinical needs first, comfort second
        strict_preferences_enabled: false
        cultural_matching_weight: 10
        language_matching_enabled: true
        clinical_override_allowed: true

    # Provider Preferences (Low - Clinical assignment priority)
    c010_provider_preferences:
      weight: "soft:75"
      description: "Clinical needs override provider preferences"
      parameters:
        provider_preference_weight: 15          # Clinical assignment priority
        schedule_preference_weight: 10
        workload_preference_weight: 25
        clinical_override_enabled: true
        professional_development_consideration: true

  # -------------------------------------------------------------------------
  # DEPENDENCY CONSTRAINTS - Task Sequencing & Prerequisites
  # -------------------------------------------------------------------------
  dependency:
    # Task Sequence Constraint (CRITICAL - 2M: Strict task ordering)
    c013_task_sequence:
      weight: "hard:2000000"
      description: "Enforce task execution sequence within care episodes"
      parameters:
        sequence_enforcement: "strict"
        same_provider_sequences: true
        sequence_buffer_minutes: 15             # Buffer between sequential tasks
        sequence_violation_penalty: "hard"
        care_episode_tracking: true

    # Task Dependencies (HIGH - 1.5M: Prerequisite requirements)
    c014_task_dependencies:
      weight: "hard:1500000"
      description: "Enforce task prerequisites and dependencies"
      parameters:
        prerequisite_enforcement: true
        cross_provider_dependencies: true
        completion_buffer_minutes: 30          # Time between dependency completion and start
        dependency_timeout_hours: 24           # Max time to complete dependent tasks
        dependency_types: ["sequence", "completion", "resource"]

# =============================================================================
# GLOBAL CONFIGURATION - Service-Level Settings
# =============================================================================

# Priority weights for visit assignment (used by urgency constraint)
priority_weights:
  EMERGENCY: 10000                            # Highest priority for medical emergencies
  CRITICAL: 1000                              # Critical medical needs
  URGENT: 100                                 # Urgent medical care
  HIGH: 50                                    # High priority routine care
  NORMAL: 10                                  # Standard skilled nursing visits
  LOW: 1                                      # Preventive/maintenance visits

# Service-level operational settings
service_settings:
  healthcare_service: "skilled_nursing"
  regulatory_level: "high"
  clinical_complexity: "advanced"

  # Risk management
  visit_completion_risk:
    minimal_time_to_shift_end_hours: 2        # 2 hours before shift end
    minimal_priority: "HIGH"                  # Only high+ priority visits considered

  # US market settings
  distance_units: "miles"                     # US market preference
  time_zone: "America/New_York"               # Default timezone

score_type: "hard_soft"
environment_mode: "REPRODUCIBLE"

# Solver configuration - longer time for complex medical scheduling
solver_config:
  time_limit_seconds: 120
  unimproved_seconds_spent_limit: 60
  best_score_limit: null
  
# Metadata for operational use
metadata:
  created_by: "system"
  created_date: "2024-01-01"
  last_modified: "2024-01-01"
  use_cases: ["skilled_nursing", "wound_care", "medication_management", "post_acute_care"]
  target_services: ["RN visits", "LPN visits", "IV therapy", "wound care", "medication administration"]
  regulatory_requirements: ["state_nursing_board", "medicare_conditions", "joint_commission"]
  complexity_level: "high_clinical"
  description: "Optimized for skilled nursing services requiring licensed professionals. Prioritizes clinical requirements, regulatory compliance, and patient safety over convenience factors."
