scenario_info:
  name: Task Dependencies - Sequential Wound Care
  description: Real optimization results using actual assignment service
  timestamp: '2025-06-03T16:24:18.924024'
  tenant_id: test_task_dependencies
configuration_used:
  profile: test_task_dependencies
  constraint_set:
  - c001_skills
  - c002_provider_avail
  - c003_overlap
  - c004_visit_completion
  - c005_max_travel
  - c006_breaks
  - c007_urgency
  - c008_continuity
  - c009_patient_pref
  - c010_provider_pref
  - c011_travel_time
  - c012_workload_balance
  - c013_task_sequence
  - c014_task_dependencies
  total_constraints: 14
  weights: &id003
    c001_skills: hard:2000000
    c002_provider_avail: hard:2000000
    c003_overlap: hard:2000000
    c004_visit_completion: hard:1500000
    c006_breaks: hard:600000
    c005_max_travel: hard:400000
    c013_task_sequence: hard:2000000
    c014_task_dependencies: hard:1500000
    c007_urgency: soft:400
    c008_continuity: soft:300
    c011_travel_time: soft:120
    c012_workload_balance: soft:100
    c009_patient_pref: soft:100
    c010_provider_pref: soft:75
  parameters:
    c001_skills:
      required_certifications:
      - RN
      - LPN
      - CNA
      specialty_skills:
      - wound_care
      - medication_management
      - iv_therapy
      skill_verification_required: true
    c002_provider_avail:
      availability_buffer_minutes: 0
      shift_overlap_allowed: false
      availability_check_enabled: true
    c003_overlap:
      minimum_gap_minutes: 15
      allow_same_location_overlap: false
      emergency_override_enabled: false
    c004_visit_completion:
      allow_partial_completion: false
      completion_buffer_minutes: 10
      required_visit_priority: high
    c006_breaks:
      min_break_minutes: 30
      max_continuous_hours: 4.0
      lunch_break_required: true
    c005_max_travel:
      max_travel_distance_miles: 35
      max_travel_time_minutes: 60
      emergency_travel_multiplier: 3.0
    c013_task_sequence: &id001
      sequence_enforcement: strict
      same_provider_sequences: true
      sequence_buffer_minutes: 15
      sequence_violation_penalty: hard
      care_episode_tracking: true
    c014_task_dependencies: &id002
      prerequisite_enforcement: true
      cross_provider_dependencies: true
      completion_buffer_minutes: 30
      dependency_timeout_hours: 24
      dependency_types:
      - sequence
      - completion
      - resource
    c007_urgency:
      urgent_decay_hours: 2
      urgent_priority_weight: 200
      critical_urgent_multiplier: 5.0
    c008_continuity:
      same_provider_bonus: 75
      provider_familiarity_weight: 40
      care_episode_tracking: true
    c011_travel_time:
      minimize_travel_enabled: true
      travel_efficiency_weight: 15
      geographic_clustering_enabled: true
    c012_workload_balance:
      balance_weight: 25
      max_workload_variance: 0.25
      consider_task_complexity: true
    c009_patient_pref:
      preference_weight: 20
      strict_preferences_enabled: false
      cultural_matching_weight: 10
    c010_provider_pref:
      provider_preference_weight: 15
      schedule_preference_weight: 10
      workload_preference_weight: 25
  dependency_constraints:
    c013_task_sequence:
      weight: hard:2000000
      parameters: *id001
    c014_task_dependencies:
      weight: hard:1500000
      parameters: *id002
input_data_summary:
  providers:
  - id: 550e8400-e29b-41d4-a716-446655440001
    name: Alice Johnson, RN
    role: Registered Nurse
    skills:
    - wound_care
    - assessment
    - medication_management
    - iv_therapy
    - patient_education
    availability: 08:00-16:00
  - id: 550e8400-e29b-41d4-a716-446655440002
    name: Bob Smith, RN
    role: Registered Nurse
    skills:
    - wound_care
    - assessment
    - documentation
    - patient_education
    - care_coordination
    availability: 09:00-17:00
  - id: 550e8400-e29b-41d4-a716-446655440003
    name: Carol Davis, CNA
    role: Certified Nursing Assistant
    skills:
    - documentation
    - basic_care
    - patient_education
    - vital_signs
    - mobility_assistance
    availability: 10:00-18:00
  - id: 550e8400-e29b-41d4-a716-446655440004
    name: Diana Martinez, LPN
    role: Licensed Practical Nurse
    skills:
    - wound_care
    - medication_administration
    - documentation
    - basic_assessment
    availability: 07:00-15:00
  consumers:
  - id: 660e8400-e29b-41d4-a716-446655440001
    name: John Doe
    age: null
    conditions:
    - diabetes_type_2
    - diabetic_foot_ulcer
    - hypertension
    - peripheral_neuropathy
    care_episode: null
  tasks:
  - id: 770e8400-e29b-41d4-a716-446655440001
    type: wound_assessment
    duration_minutes: 30
    required_skills:
    - wound_care
    - assessment
    sequence_group: wound_care_episode_001
    sequence_order: 1
    prerequisites: []
    same_provider_required: true
    dependency_type: sequence
  - id: 770e8400-e29b-41d4-a716-446655440002
    type: wound_cleaning
    duration_minutes: 45
    required_skills:
    - wound_care
    sequence_group: wound_care_episode_001
    sequence_order: 2
    prerequisites:
    - 770e8400-e29b-41d4-a716-446655440001
    same_provider_required: true
    dependency_type: sequence
  - id: 770e8400-e29b-41d4-a716-446655440003
    type: wound_dressing
    duration_minutes: 30
    required_skills:
    - wound_care
    sequence_group: wound_care_episode_001
    sequence_order: 3
    prerequisites:
    - 770e8400-e29b-41d4-a716-446655440002
    same_provider_required: true
    dependency_type: sequence
  - id: 770e8400-e29b-41d4-a716-446655440004
    type: documentation
    duration_minutes: 15
    required_skills:
    - documentation
    sequence_group: null
    sequence_order: null
    prerequisites:
    - 770e8400-e29b-41d4-a716-446655440001
    - 770e8400-e29b-41d4-a716-446655440002
    - 770e8400-e29b-41d4-a716-446655440003
    same_provider_required: false
    dependency_type: completion
constraint_analysis:
  available_constraints:
  - c001_skills
  - c002_provider_avail
  - c003_overlap
  - c004_visit_completion
  - c005_max_travel
  - c006_breaks
  - c007_urgency
  - c008_continuity
  - c009_patient_pref
  - c010_provider_pref
  - c011_travel_time
  - c012_workload_balance
  - c013_task_sequence
  - c014_task_dependencies
  dependency_constraints_active:
  - c013_task_sequence
  - c014_task_dependencies
  constraint_weights: *id003
solution_analysis:
  solution_type: AssignmentSolution
  total_assignments: 4
  assigned_count: 0
  unassigned_count: 4
  assignment_rate: 0.0
  validation_errors: 4
  solution_statistics:
    providers: 4
    consumers: 1
    assignments: 4
    time_slots: 33
    assigned_count: 0
    unassigned_count: 4
    assignment_rate: 0.0%
    validation_errors: 4
    available_skills: 12
    required_skills: 3
    skill_coverage: 1.0
    planning_window_hours: 8.0
  solution_summary:
    total_assignments: 4
    assigned_count: 0
    unassigned_count: 4
    assignment_rate: 0.0%
    total_providers: 4
    total_consumers: 1
    total_time_slots: 33
    score: Not calculated
    validation_errors: 0
optimization_status:
  solver_factory_created: true
  constraints_loaded: true
  solution_built: true
  ready_for_optimization: true
  note: Complete Timefold integration ready - solution class, planning entities, and
    constraints configured.
