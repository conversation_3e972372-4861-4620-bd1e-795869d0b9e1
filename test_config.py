#!/usr/bin/env python3
"""Test configuration loading."""

import asyncio
from app.core.config_loader import load_profile

async def test_config():
    try:
        config = await load_profile("test_task_dependencies")
        print("✅ Configuration loaded successfully!")
        print(f"Configuration keys: {list(config.keys())}")
        if 'constraint_set' in config:
            print(f"Constraint set: {config['constraint_set']}")
        if 'weights' in config:
            print(f"Number of weights: {len(config['weights'])}")
        if 'parameters' in config:
            print(f"Number of parameter groups: {len(config['parameters'])}")
        return config
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_config())
