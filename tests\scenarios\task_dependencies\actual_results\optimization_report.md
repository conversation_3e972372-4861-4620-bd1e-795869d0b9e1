# Task Dependencies Optimization Report

## Scenario Overview
- **Name**: Task Dependencies - Sequential Wound Care
- **Description**: Real optimization results using actual assignment service
- **Timestamp**: 2025-06-03T16:24:18.924024
- **Tenant ID**: test_task_dependencies

## Configuration Analysis

### Profile Used
- **Configuration Profile**: `test_task_dependencies`
- **Total Constraint Groups**: 14
- **Total Constraints**: 14

### Dependency Constraints Configuration

#### c013_task_sequence
- **Weight**: `hard:2000000`
- **Description**: Task dependencies scenario test
- **Parameters**: {'sequence_enforcement': 'strict', 'same_provider_sequences': True, 'sequence_buffer_minutes': 15, 'sequence_violation_penalty': 'hard', 'care_episode_tracking': True}

#### c014_task_dependencies
- **Weight**: `hard:1500000`
- **Description**: Task dependencies scenario test
- **Parameters**: {'prerequisite_enforcement': True, 'cross_provider_dependencies': True, 'completion_buffer_minutes': 30, 'dependency_timeout_hours': 24, 'dependency_types': ['sequence', 'completion', 'resource']}

## Input Data Analysis

### Providers (4)

#### <PERSON>, R<PERSON> (Registered Nurse)
- **Skills**: wound_care, assessment, medication_management, iv_therapy, patient_education
- **Availability**: 08:00-16:00

#### Bob Smith, RN (Registered Nurse)
- **Skills**: wound_care, assessment, documentation, patient_education, care_coordination
- **Availability**: 09:00-17:00

#### Carol Davis, CNA (Certified Nursing Assistant)
- **Skills**: documentation, basic_care, patient_education, vital_signs, mobility_assistance
- **Availability**: 10:00-18:00

#### Diana Martinez, LPN (Licensed Practical Nurse)
- **Skills**: wound_care, medication_administration, documentation, basic_assessment
- **Availability**: 07:00-15:00

### Tasks (4)

#### Wound_Assessment (Duration: 30min)
- **Required Skills**: wound_care, assessment
- **Sequence Group**: wound_care_episode_001
- **Sequence Order**: 1
- **Prerequisites**: 0 tasks
- **Same Provider Required**: True
- **Dependency Type**: sequence

#### Wound_Cleaning (Duration: 45min)
- **Required Skills**: wound_care
- **Sequence Group**: wound_care_episode_001
- **Sequence Order**: 2
- **Prerequisites**: 1 tasks
- **Same Provider Required**: True
- **Dependency Type**: sequence

#### Wound_Dressing (Duration: 30min)
- **Required Skills**: wound_care
- **Sequence Group**: wound_care_episode_001
- **Sequence Order**: 3
- **Prerequisites**: 1 tasks
- **Same Provider Required**: True
- **Dependency Type**: sequence

#### Documentation (Duration: 15min)
- **Required Skills**: documentation
- **Sequence Group**: None
- **Sequence Order**: None
- **Prerequisites**: 3 tasks
- **Same Provider Required**: False
- **Dependency Type**: completion

## Constraint Analysis

### Available Constraints
Total discovered: 14

- `c001_skills` (Weight: hard:2000000)
- `c002_provider_avail` (Weight: hard:2000000)
- `c003_overlap` (Weight: hard:2000000)
- `c004_visit_completion` (Weight: hard:1500000)
- `c005_max_travel` (Weight: hard:400000)
- `c006_breaks` (Weight: hard:600000)
- `c007_urgency` (Weight: soft:400)
- `c008_continuity` (Weight: soft:300)
- `c009_patient_pref` (Weight: soft:100)
- `c010_provider_pref` (Weight: soft:75)
- `c011_travel_time` (Weight: soft:120)
- `c012_workload_balance` (Weight: soft:100)
- `c013_task_sequence` (Weight: hard:2000000)
- `c014_task_dependencies` (Weight: hard:1500000)

### Active Dependency Constraints
c013_task_sequence, c014_task_dependencies

## Optimization Status
- ✅ Solver factory created successfully
- ✅ Constraints loaded and validated
- ✅ Input data prepared and validated
- ✅ Ready for optimization execution

## Next Steps
1. **Execute Optimization**: Run the solver with prepared data
2. **Analyze Results**: Review constraint violations and assignments
3. **Validate Dependencies**: Ensure task sequencing is respected
4. **Generate Schedule**: Create final provider schedules

---
*Report generated by Assignment Service Test Suite*
*Configuration: test_task_dependencies*
