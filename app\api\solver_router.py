
"""
Solver API router for assignment optimization.

Provides endpoints for solving assignment problems with tenant-specific configurations.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from uuid import UUID

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from app.solver.factory import build_factory, get_available_constraints, validate_constraint_configuration
from app.core.db import fetch_problem_for_tenant
from app.core.rls import require_tenant
from app.core.config_loader import load_profile
from app.domain.models import Provider, Task, Consumer

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/solver", tags=["solver"])


class SolveRequest(BaseModel):
    """Request model for solving optimization problems."""
    time_limit_seconds: Optional[int] = Field(default=60, ge=1, le=3600)
    unimproved_seconds_limit: Optional[int] = Field(default=30, ge=1, le=1800)
    best_score_limit: Optional[str] = None


class SolveResponse(BaseModel):
    """Response model for optimization solutions."""
    tenant_id: str
    solution_id: str
    status: str
    score: Optional[str] = None
    solve_time_seconds: float
    assignments: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    warnings: List[str] = []


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    available_constraints: List[str]
    version: str = "1.0"


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint for the solver service.

    Returns:
        Service health status and available constraints
    """
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        available_constraints=get_available_constraints()
    )


@router.get("/constraints")
async def list_available_constraints():
    """
    List all available constraint providers.

    Returns:
        Dictionary of constraint names and descriptions
    """
    constraints = get_available_constraints()

    # Add descriptions for each constraint
    constraint_descriptions = {
        "skills": "Ensures tasks are assigned to providers with required skills",
        "provider_avail": "Checks provider availability during task times",
        "overlap": "Prevents overlapping tasks for the same provider",
        "visit_completion": "Ensures all required visits are completed",
        "max_travel": "Limits maximum travel distance/time between tasks",
        "breaks": "Enforces break requirements between tasks",
        "continuity": "Promotes continuity of care with same providers",
        "patient_pref": "Considers patient preferences for providers and timing",
        "provider_pref": "Considers provider preferences for tasks and schedules",
        "travel_time": "Optimizes travel time and routing efficiency",
        "workload_balance": "Balances workload among providers",
        "urgency": "Prioritizes urgent tasks appropriately"
    }

    return {
        "constraints": [
            {
                "name": constraint,
                "description": constraint_descriptions.get(constraint, "No description available")
            }
            for constraint in constraints
        ]
    }


@router.post("/validate-config/{tenant_id}")
async def validate_configuration(
    tenant_id: str,
    constraint_set: List[str],
    current_tenant: str = Depends(require_tenant)
):
    """
    Validate a constraint configuration for a tenant.

    Args:
        tenant_id: The tenant identifier
        constraint_set: List of constraint names to validate
        current_tenant: Current authenticated tenant

    Returns:
        Validation results
    """
    # Ensure tenant access
    if current_tenant != tenant_id:
        raise HTTPException(status_code=403, detail="Access denied for tenant")

    # Validate constraints
    errors = validate_constraint_configuration(constraint_set)

    return {
        "tenant_id": tenant_id,
        "constraint_set": constraint_set,
        "valid": len(errors) == 0,
        "errors": errors
    }


@router.get("/config/{tenant_id}")
async def get_tenant_configuration(
    tenant_id: str,
    current_tenant: str = Depends(require_tenant)
):
    """
    Get the current configuration for a tenant.

    Args:
        tenant_id: The tenant identifier
        current_tenant: Current authenticated tenant

    Returns:
        Tenant configuration
    """
    # Ensure tenant access
    if current_tenant != tenant_id:
        raise HTTPException(status_code=403, detail="Access denied for tenant")

    try:
        config = await load_profile(tenant_id)
        return {
            "tenant_id": tenant_id,
            "config": config
        }
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"Configuration not found for tenant: {tenant_id}")
    except Exception as e:
        logger.error(f"Error loading config for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to load configuration")


@router.post("/solve/{tenant_id}", response_model=SolveResponse)
async def solve_schedule(
    tenant_id: str,
    solve_request: SolveRequest = SolveRequest(),
    use_mock_data: bool = Query(default=False, description="Use mock data instead of database"),
    current_tenant: str = Depends(require_tenant)
):
    """
    Solve an assignment optimization problem for a tenant.

    Args:
        tenant_id: The tenant identifier
        solve_request: Solving parameters
        use_mock_data: Whether to use mock data for testing
        current_tenant: Current authenticated tenant

    Returns:
        Optimization solution with assignments and statistics
    """
    # Ensure tenant access
    if current_tenant != tenant_id:
        raise HTTPException(status_code=403, detail="Access denied for tenant")

    solve_start_time = datetime.now()
    warnings = []

    try:
        # Load problem data
        if use_mock_data:
            resources, tasks, consumers = await _fetch_mock_problem(tenant_id)
            warnings.append("Using mock data for demonstration")
        else:
            try:
                resources, tasks, consumers = await fetch_problem_for_tenant(tenant_id)
            except Exception as e:
                logger.warning(f"Failed to load real data for {tenant_id}, falling back to mock: {e}")
                resources, tasks, consumers = await _fetch_mock_problem(tenant_id)
                warnings.append("Fell back to mock data due to database error")

        if not resources:
            raise HTTPException(status_code=400, detail="No providers available for scheduling")

        if not tasks:
            raise HTTPException(status_code=400, detail="No tasks to schedule")

        # Build solver
        try:
            factory = await build_factory(tenant_id)
            solver = factory.build_solver()
        except Exception as e:
            logger.error(f"Failed to build solver for tenant {tenant_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Solver configuration error: {str(e)}")

        # Create planning problem
        problem = _create_planning_problem(resources, tasks, consumers)

        # Solve the problem
        try:
            solution = solver.solve(
                problem,
                time_limit_seconds=solve_request.time_limit_seconds
            )
        except Exception as e:
            logger.error(f"Solver execution failed for tenant {tenant_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Solver execution error: {str(e)}")

        # Calculate solve time
        solve_time = (datetime.now() - solve_start_time).total_seconds()

        # Process solution
        assignments, statistics = _process_solution(solution, resources, tasks, consumers)

        # Generate solution ID
        solution_id = f"{tenant_id}_{int(solve_start_time.timestamp())}"

        return SolveResponse(
            tenant_id=tenant_id,
            solution_id=solution_id,
            status="completed",
            score=str(solution.score) if hasattr(solution, 'score') else None,
            solve_time_seconds=solve_time,
            assignments=assignments,
            statistics=statistics,
            warnings=warnings
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error solving for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _fetch_mock_problem(tenant_id: str):
    """
    Generate mock problem data for testing.

    Returns:
        Tuple of (resources, tasks, consumers)
    """
    import random
    from datetime import datetime, timedelta

    # Create mock providers
    resources = [
        Provider(
            id=UUID(int=1),
            name='Nurse Alice',
            skills=['RN', 'wound_care'],
            capacity=1,
            properties={
                'experience_level': 'senior',
                'available_hours_per_day': 8,
                'preferences': {
                    'preferred_working_hours': {'start': 8, 'end': 16},
                    'max_tasks_per_day': 6
                }
            }
        ),
        Provider(
            id=UUID(int=2),
            name='Therapist Bob',
            skills=['PT', 'mobility'],
            capacity=1,
            properties={
                'experience_level': 'intermediate',
                'available_hours_per_day': 8,
                'preferences': {
                    'preferred_working_hours': {'start': 9, 'end': 17},
                    'max_tasks_per_day': 5
                }
            }
        )
    ]

    # Create mock consumers
    consumers = [
        Consumer(
            id=UUID(int=10),
            name='Patient Smith',
            properties={
                'preferred_times': [{'start': 9, 'end': 12}],
                'preferred_providers': [UUID(int=1)]
            }
        ),
        Consumer(
            id=UUID(int=11),
            name='Patient Jones',
            properties={
                'preferred_times': [{'start': 14, 'end': 17}]
            }
        )
    ]

    # Create mock tasks
    base_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    tasks = [
        Task(
            id=UUID(int=20),
            consumer_id=consumers[0].id,
            required_skills=['RN'],
            duration_min=60,
            earliest_start=base_time,
            latest_end=base_time + timedelta(hours=6),
            urgent=False,
            properties={
                'task_type': 'medication_administration',
                'complexity_level': 3,
                'location': {'lat': 40.7128, 'lng': -74.0060}
            }
        ),
        Task(
            id=UUID(int=21),
            consumer_id=consumers[1].id,
            required_skills=['PT'],
            duration_min=45,
            earliest_start=base_time + timedelta(hours=2),
            latest_end=base_time + timedelta(hours=8),
            urgent=True,
            properties={
                'task_type': 'physical_therapy',
                'complexity_level': 4,
                'location': {'lat': 40.7589, 'lng': -73.9851}
            }
        )
    ]

    logger.info(f"Generated mock problem for {tenant_id}: {len(resources)} providers, {len(tasks)} tasks")

    return resources, tasks, consumers


def _create_planning_problem(resources: List[Provider], tasks: List[Task], consumers: List[Consumer]) -> Dict[str, Any]:
    """
    Create a planning problem structure for the solver.

    Args:
        resources: List of available resources
        tasks: List of tasks to assign
        consumers: List of consumers

    Returns:
        Planning problem dictionary
    """
    return {
        'resources': [r.dict() for r in resources],
        'tasks': [t.dict() for t in tasks],
        'consumers': [c.dict() for c in consumers],
        'problem_metadata': {
            'resource_count': len(resources),
            'task_count': len(tasks),
            'consumer_count': len(consumers),
            'created_at': datetime.now().isoformat()
        }
    }


def _process_solution(solution, resources: List[Provider], tasks: List[Task], consumers: List[Consumer]):
    """
    Process the solver solution into a structured response.

    Args:
        solution: Solver solution object
        resources: Original resources
        tasks: Original tasks
        consumers: Original consumers

    Returns:
        Tuple of (assignments, statistics)
    """
    # Extract assignments (this would depend on the actual solution structure)
    assignments = []

    # For now, create a mock assignment structure
    for i, task in enumerate(tasks):
        resource = resources[i % len(resources)]  # Simple round-robin assignment

        assignments.append({
            'task_id': str(task.id),
            'resource_id': str(resource.id),
            'consumer_id': str(task.consumer_id),
            'task_name': task.properties.get('task_type', 'Unknown'),
            'resource_name': resource.name,
            'scheduled_start': task.earliest_start.isoformat() if task.earliest_start else None,
            'scheduled_end': (task.earliest_start + timedelta(minutes=task.duration_min)).isoformat() if task.earliest_start else None,
            'duration_minutes': task.duration_min,
            'status': 'assigned'
        })

    # Calculate statistics
    statistics = {
        'total_tasks': len(tasks),
        'assigned_tasks': len(assignments),
        'unassigned_tasks': len(tasks) - len(assignments),
        'total_resources': len(resources),
        'utilized_resources': len(set(a['resource_id'] for a in assignments)),
        'assignment_rate': len(assignments) / len(tasks) if tasks else 0,
        'resource_utilization': len(set(a['resource_id'] for a in assignments)) / len(resources) if resources else 0
    }

    return assignments, statistics
