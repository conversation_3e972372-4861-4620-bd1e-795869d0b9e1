# =============================================================================
# TEST TASK DEPENDENCIES CONFIGURATION
# =============================================================================
# Configuration for testing task dependencies and sequencing scenarios
# This configuration follows the expected format for the config loader

# Profile metadata (required)
profile:
  name: "test_task_dependencies"
  description: "Test configuration for task dependencies scenario"
  healthcare_service: "skilled_nursing"
  regulatory_level: "high"
  clinical_complexity: "advanced"

# =============================================================================
# CONSTRAINT CONFIGURATION
# =============================================================================

# List of constraint names to include
constraint_set:
  - c001_skills
  - c002_provider_avail
  - c003_overlap
  - c004_visit_completion
  - c005_max_travel
  - c006_breaks
  - c007_urgency
  - c008_continuity
  - c009_patient_pref
  - c010_provider_pref
  - c011_travel_time
  - c012_workload_balance
  - c013_task_sequence
  - c014_task_dependencies

# Constraint weights
weights:
  c001_skills: "hard:2000000"
  c002_provider_avail: "hard:2000000"
  c003_overlap: "hard:2000000"
  c004_visit_completion: "hard:1500000"
  c005_max_travel: "hard:400000"
  c006_breaks: "hard:600000"
  c007_urgency: "soft:400"
  c008_continuity: "soft:300"
  c009_patient_pref: "soft:100"
  c010_provider_pref: "soft:75"
  c011_travel_time: "soft:120"
  c012_workload_balance: "soft:100"
  c013_task_sequence: "hard:2000000"
  c014_task_dependencies: "hard:1500000"

# Configuration parameters for constraints
parameters:
  # Skills constraint parameters
  c001_skills:
    required_certifications: ["RN", "LPN", "CNA"]
    specialty_skills: ["wound_care", "medication_management", "iv_therapy"]
    skill_verification_required: true

  # Provider availability parameters
  c002_provider_avail:
    availability_buffer_minutes: 0
    shift_overlap_allowed: false
    availability_check_enabled: true

  # Overlap prevention parameters
  c003_overlap:
    minimum_gap_minutes: 15
    allow_same_location_overlap: false
    emergency_override_enabled: false

  # Visit completion parameters
  c004_visit_completion:
    allow_partial_completion: false
    completion_buffer_minutes: 10
    required_visit_priority: "high"

  # Travel limits parameters
  c005_max_travel:
    max_travel_distance_miles: 35
    max_travel_time_minutes: 60
    emergency_travel_multiplier: 3.0

  # Break requirements parameters
  c006_breaks:
    min_break_minutes: 30
    max_continuous_hours: 4.0
    lunch_break_required: true

  # Urgency parameters
  c007_urgency:
    urgent_decay_hours: 2
    urgent_priority_weight: 200
    critical_urgent_multiplier: 5.0

  # Continuity parameters
  c008_continuity:
    same_provider_bonus: 75
    provider_familiarity_weight: 40
    care_episode_tracking: true

  # Patient preferences parameters
  c009_patient_pref:
    preference_weight: 20
    strict_preferences_enabled: false
    cultural_matching_weight: 10

  # Provider preferences parameters
  c010_provider_pref:
    provider_preference_weight: 15
    schedule_preference_weight: 10
    workload_preference_weight: 25

  # Travel time parameters
  c011_travel_time:
    minimize_travel_enabled: true
    travel_efficiency_weight: 15
    geographic_clustering_enabled: true

  # Workload balance parameters
  c012_workload_balance:
    balance_weight: 25
    max_workload_variance: 0.25
    consider_task_complexity: true

  # Task sequence parameters
  c013_task_sequence:
    sequence_enforcement: "strict"
    same_provider_sequences: true
    sequence_buffer_minutes: 15
    sequence_violation_penalty: "hard"
    care_episode_tracking: true

  # Task dependencies parameters
  c014_task_dependencies:
    prerequisite_enforcement: true
    cross_provider_dependencies: true
    completion_buffer_minutes: 30
    dependency_timeout_hours: 24
    dependency_types: ["sequence", "completion", "resource"]

# =============================================================================
# GLOBAL CONFIGURATION
# =============================================================================

# Priority weights for visit assignment
priority_weights:
  EMERGENCY: 10000
  CRITICAL: 1000
  URGENT: 100
  HIGH: 50
  NORMAL: 10
  LOW: 1

# Service-level operational settings
service_settings:
  healthcare_service: "skilled_nursing"
  regulatory_level: "high"
  clinical_complexity: "advanced"

score_type: "hard_soft"
environment_mode: "REPRODUCIBLE"

# Solver configuration - focused on task dependencies testing
solver_config:
  time_limit_seconds: 120
  unimproved_seconds_spent_limit: 60
  best_score_limit: null

# Metadata for operational use
metadata:
  created_by: "test_suite"
  created_date: "2024-01-15"
  last_modified: "2024-01-15"
  use_cases: ["task_dependencies", "sequential_care", "wound_care", "care_episodes"]
  target_services: ["wound assessment", "wound cleaning", "wound dressing", "documentation"]
  regulatory_requirements: ["skilled_nursing_standards", "state_licensing", "care_coordination"]
  complexity_level: "high_clinical"
  description: "Test configuration for validating task dependencies and sequencing constraints in healthcare workflows. Focuses on sequential wound care episodes with strict task ordering and provider continuity requirements."
