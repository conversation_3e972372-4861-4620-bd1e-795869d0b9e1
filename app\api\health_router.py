"""
Health check endpoints for assignment service monitoring.

Provides comprehensive health checks for database, solver, cache,
and external dependencies.
"""

import asyncio
import logging
import time
from typing import Dict, Any, List
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, Response
from pydantic import BaseModel

from app.core.db import get_db
from app.core.metrics import metrics
from app.solver.factory import get_cache_stats, build_factory
from app.core.config_loader import load_profile

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/health", tags=["health"])


class HealthStatus(BaseModel):
    """Health check status model."""
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    version: str = "1.0.0"
    uptime_seconds: float
    checks: Dict[str, Dict[str, Any]]


class ComponentHealth(BaseModel):
    """Individual component health model."""
    status: str
    message: str
    duration_ms: float
    details: Dict[str, Any] = {}


class HealthChecker:
    """Centralized health checking logic."""
    
    def __init__(self):
        self.start_time = time.time()
    
    async def check_database(self) -> ComponentHealth:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            from sqlalchemy import text
            async for session in get_db():
                # Simple connectivity test
                result = await session.execute(text("SELECT 1"))
                await result.fetchone()

                # Check if we can access main tables
                result = await session.execute(text("SELECT COUNT(*) FROM tasks LIMIT 1"))
                await result.fetchone()
                break  # Only need one iteration

            duration_ms = (time.time() - start_time) * 1000

            if duration_ms > 1000:  # Slow response
                return ComponentHealth(
                    status="degraded",
                    message=f"Database responding slowly ({duration_ms:.1f}ms)",
                    duration_ms=duration_ms,
                    details={"response_time_threshold_ms": 1000}
                )

            return ComponentHealth(
                status="healthy",
                message="Database connectivity OK",
                duration_ms=duration_ms
            )
                
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Database health check failed: {e}")
            return ComponentHealth(
                status="unhealthy",
                message=f"Database connection failed: {str(e)}",
                duration_ms=duration_ms,
                details={"error": str(e)}
            )
    
    async def check_solver_factory(self) -> ComponentHealth:
        """Check solver factory and constraint loading."""
        start_time = time.time()
        
        try:
            # Test constraint provider discovery
            from app.solver.factory import get_provider_registry
            registry = get_provider_registry()
            
            if not registry:
                return ComponentHealth(
                    status="unhealthy",
                    message="No constraint providers discovered",
                    duration_ms=(time.time() - start_time) * 1000,
                    details={"provider_count": 0}
                )
            
            # Test solver factory creation with a test tenant
            test_tenant = "health_check_tenant"
            try:
                # This should use cached factory if available
                factory = await build_factory(test_tenant)
                
                duration_ms = (time.time() - start_time) * 1000
                
                return ComponentHealth(
                    status="healthy",
                    message="Solver factory operational",
                    duration_ms=duration_ms,
                    details={
                        "provider_count": len(registry),
                        "providers": list(registry.keys())
                    }
                )
                
            except FileNotFoundError:
                # Expected for health check tenant
                duration_ms = (time.time() - start_time) * 1000
                return ComponentHealth(
                    status="healthy",
                    message="Solver factory operational (no test config)",
                    duration_ms=duration_ms,
                    details={
                        "provider_count": len(registry),
                        "providers": list(registry.keys())
                    }
                )
                
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Solver factory health check failed: {e}")
            return ComponentHealth(
                status="unhealthy",
                message=f"Solver factory error: {str(e)}",
                duration_ms=duration_ms,
                details={"error": str(e)}
            )
    
    async def check_cache_performance(self) -> ComponentHealth:
        """Check cache performance and efficiency."""
        start_time = time.time()
        
        try:
            cache_stats = get_cache_stats()
            duration_ms = (time.time() - start_time) * 1000
            
            cache_size = cache_stats['cache_size']
            cached_tenants = cache_stats['cached_tenants']
            
            # Check for cache health indicators
            status = "healthy"
            message = "Cache operational"
            
            if cache_size == 0:
                status = "degraded"
                message = "Cache empty (cold start)"
            elif cache_size > 100:  # Arbitrary threshold
                status = "degraded"
                message = f"Cache size large ({cache_size} entries)"
            
            # Check for stale entries
            stale_entries = 0
            current_time = time.time()
            for tenant_id, entry_info in cache_stats['cache_entries'].items():
                if entry_info['age_seconds'] > 3600:  # 1 hour
                    stale_entries += 1
            
            if stale_entries > 0:
                status = "degraded"
                message += f", {stale_entries} stale entries"
            
            return ComponentHealth(
                status=status,
                message=message,
                duration_ms=duration_ms,
                details={
                    "cache_size": cache_size,
                    "cached_tenants": cached_tenants,
                    "stale_entries": stale_entries
                }
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Cache health check failed: {e}")
            return ComponentHealth(
                status="unhealthy",
                message=f"Cache check error: {str(e)}",
                duration_ms=duration_ms,
                details={"error": str(e)}
            )
    
    async def check_configuration(self) -> ComponentHealth:
        """Check configuration loading and validation."""
        start_time = time.time()
        
        try:
            # Test configuration directory access
            from app.core.config_loader import CONFIG_DIR
            
            if not CONFIG_DIR.exists():
                return ComponentHealth(
                    status="degraded",
                    message="Configuration directory not found",
                    duration_ms=(time.time() - start_time) * 1000,
                    details={"config_dir": str(CONFIG_DIR)}
                )
            
            # Count available configuration files
            config_files = list(CONFIG_DIR.glob("*.yml"))
            
            # Test loading a configuration if available
            if config_files:
                test_file = config_files[0]
                tenant_id = test_file.stem
                
                try:
                    config = await load_profile(tenant_id)
                    
                    duration_ms = (time.time() - start_time) * 1000
                    return ComponentHealth(
                        status="healthy",
                        message="Configuration loading operational",
                        duration_ms=duration_ms,
                        details={
                            "config_files": len(config_files),
                            "test_tenant": tenant_id,
                            "constraint_count": len(config.get('constraint_set', []))
                        }
                    )
                    
                except Exception as e:
                    duration_ms = (time.time() - start_time) * 1000
                    return ComponentHealth(
                        status="degraded",
                        message=f"Configuration loading error: {str(e)}",
                        duration_ms=duration_ms,
                        details={
                            "config_files": len(config_files),
                            "error": str(e)
                        }
                    )
            else:
                duration_ms = (time.time() - start_time) * 1000
                return ComponentHealth(
                    status="degraded",
                    message="No configuration files found",
                    duration_ms=duration_ms,
                    details={"config_files": 0}
                )
                
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(f"Configuration health check failed: {e}")
            return ComponentHealth(
                status="unhealthy",
                message=f"Configuration check error: {str(e)}",
                duration_ms=duration_ms,
                details={"error": str(e)}
            )
    
    async def run_all_checks(self) -> HealthStatus:
        """Run all health checks and aggregate results."""
        start_time = time.time()
        
        # Run all checks concurrently
        checks = await asyncio.gather(
            self.check_database(),
            self.check_solver_factory(),
            self.check_cache_performance(),
            self.check_configuration(),
            return_exceptions=True
        )
        
        check_names = ["database", "solver_factory", "cache", "configuration"]
        check_results = {}
        
        overall_status = "healthy"
        
        for i, check in enumerate(checks):
            name = check_names[i]
            
            if isinstance(check, Exception):
                check_results[name] = ComponentHealth(
                    status="unhealthy",
                    message=f"Health check failed: {str(check)}",
                    duration_ms=0,
                    details={"error": str(check)}
                ).model_dump()
                overall_status = "unhealthy"
            else:
                check_results[name] = check.model_dump()
                
                if check.status == "unhealthy":
                    overall_status = "unhealthy"
                elif check.status == "degraded" and overall_status == "healthy":
                    overall_status = "degraded"
        
        uptime_seconds = time.time() - self.start_time
        
        return HealthStatus(
            status=overall_status,
            timestamp=datetime.now(),
            uptime_seconds=uptime_seconds,
            checks=check_results
        )


# Global health checker instance
health_checker = HealthChecker()


@router.get("/", response_model=HealthStatus)
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Returns overall service health including all components.
    """
    try:
        health_status = await health_checker.run_all_checks()
        
        # Set HTTP status code based on health
        status_code = 200
        if health_status.status == "degraded":
            status_code = 200  # Still operational
        elif health_status.status == "unhealthy":
            status_code = 503  # Service unavailable
        
        return Response(
            content=health_status.model_dump_json(),
            status_code=status_code,
            media_type="application/json"
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check error: {str(e)}")


@router.get("/live")
async def liveness_probe():
    """
    Kubernetes liveness probe endpoint.
    
    Simple check that the service is running.
    """
    return {"status": "alive", "timestamp": datetime.now()}


@router.get("/ready")
async def readiness_probe():
    """
    Kubernetes readiness probe endpoint.
    
    Checks if service is ready to handle requests.
    """
    try:
        # Quick database check
        db_check = await health_checker.check_database()
        
        if db_check.status == "unhealthy":
            raise HTTPException(status_code=503, detail="Database not ready")
        
        return {"status": "ready", "timestamp": datetime.now()}
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")


@router.get("/metrics")
async def metrics_endpoint():
    """
    Prometheus metrics endpoint.
    
    Returns metrics in Prometheus format.
    """
    try:
        metrics_data = metrics.get_metrics()
        return Response(content=metrics_data, media_type="text/plain")
    except Exception as e:
        logger.error(f"Metrics endpoint failed: {e}")
        raise HTTPException(status_code=500, detail=f"Metrics error: {str(e)}")


@router.get("/cache/stats")
async def cache_stats():
    """
    Cache statistics endpoint for debugging.
    """
    try:
        stats = get_cache_stats()
        return stats
    except Exception as e:
        logger.error(f"Cache stats failed: {e}")
        raise HTTPException(status_code=500, detail=f"Cache stats error: {str(e)}")
