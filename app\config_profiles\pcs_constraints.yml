# =============================================================================
# PERSONAL CARE SERVICES CONFIGURATION
# =============================================================================
# Healthcare Service: Personal care services focused on activities of daily living
# Focus: Relationship-based care, comfort, trust, and caregiver satisfaction
# =============================================================================

# Profile metadata
profile:
  name: "pcs_constraints"
  description: "Personal Care Services focused on activities of daily living and companion care"
  healthcare_service: "personal_care_services"
  regulatory_level: "standard"
  clinical_complexity: "basic"

# =============================================================================
# CONSTRAINT CONFIGURATION - Optimized for Personal Care Services
# =============================================================================

# Constraint weights organized by priority and type
constraints:
  # -------------------------------------------------------------------------
  # HARD CONSTRAINTS - Basic Requirements
  # -------------------------------------------------------------------------
  hard:
    # Skills & Qualifications (HIGH - 1M: Basic care certifications)
    c001_skills:
      weight: "hard:1000000"                    # Basic care skills and certifications
      description: "Basic care skills and certifications required"

    # Provider Availability (HIGH - 1M: Regular scheduling)
    c002_provider_availability:
      start_time_weight: "hard:1000000"         # Regular scheduling patterns
      end_time_weight: "hard:1000000"           # Scheduled availability
      description: "Regular scheduling patterns and availability"

    # Overlap Prevention (MODERATE - 800K: Flexible scheduling)
    c003_overlap:
      weight: "hard:800000"                     # Some flexibility allowed
      description: "Flexible scheduling allowed for efficiency"

    # Visit Completion (MODERATE - 600K/400K: Consistency for ADLs)
    c004_visit_completion:
      required_unassigned_weight: "hard:600000"     # Consistency important for ADLs
      time_window_violation_weight: "hard:400000"   # Flexible timing
      description: "Consistency important for activities of daily living"

    # Break Requirements (LOW - 400K: Regular work requirements)
    c006_breaks:
      weight: "hard:400000"                     # Standard work requirements
      description: "Regular work requirements and labor compliance"

    # Travel Limits (LOW - 200K: Cost control)
    c005_max_travel:
      distance_weight: "hard:200000"            # Cost-effective coverage
      description: "Cost-effective coverage with reasonable travel limits"

  # -------------------------------------------------------------------------
  # SOFT CONSTRAINTS - Relationship & Satisfaction Focus
  # -------------------------------------------------------------------------
  soft:
    # Continuity of Care (Critical - Relationship-based care essential)
    c008_continuity:
      weight: "soft:400"                        # Relationship-based care essential
      description: "Relationship-based care and trust building"

    # Patient Preferences (Critical - Comfort and trust essential)
    c009_patient_preferences:
      weight: "soft:300"                        # Comfort and trust essential
      description: "Comfort and trust essential for personal care"

    # Provider Preferences (High - Job satisfaction important)
    c010_provider_preferences:
      weight: "soft:200"                        # Job satisfaction for retention
      description: "Job satisfaction important for caregiver retention"

    # Urgency & Priority (Moderate - Routine care focus)
    c007_urgency:
      weight: "soft:150"                        # Some urgent needs
      description: "Routine care focus with occasional urgent needs"

    # Workload Balance (High - Fair scheduling for retention)
    c012_workload_balance:
      weight: "soft:150"                        # Fair scheduling for retention
      description: "Fair scheduling for caregiver retention and satisfaction"

    # Travel Optimization (Moderate - Cost efficiency)
    c011_travel_time:
      weight: "soft:100"                        # Cost efficiency important
      description: "Cost efficiency and reasonable travel optimization"

# =============================================================================
# HEALTHCARE-SPECIFIC PARAMETERS
# =============================================================================
parameters:
  # Clinical Requirements (Basic Care)
  clinical:
    skill_verification_required: true
    license_expiry_check: false
    certification_levels: ["CNA", "HHA", "PCA"]
    specialty_skills: ["adl_assistance", "companionship", "meal_preparation", "medication_reminders"]
    background_check_required: true

  # Travel and Geography (Cost-Effective)
  travel:
    max_travel_distance_miles: 25             # Cost-effective coverage
    max_travel_time_minutes: 45
    emergency_travel_multiplier: 2.0
    cost_optimization_enabled: true

  # Regulatory Compliance (Standard)
  regulatory:
    min_break_minutes: 30                     # Standard work conditions
    max_continuous_hours: 4.0
    lunch_break_required: true
    lunch_break_duration_minutes: 30
    flexible_break_scheduling: true

  # Quality of Care (Relationship-Focused)
  quality:
    continuity_bonus_weight: 100              # Relationship-based care
    same_provider_preference: true
    trust_building_enabled: true
    patient_safety_priority: "standard"
    personal_compatibility_focus: true

  # Urgency and Priority (Routine Care Focus)
  urgency:
    urgent_decay_hours: 8                     # Routine care with occasional urgent needs
    critical_multiplier: 2.0
    routine_care_priority: true
    priority_levels: ["routine", "preferred", "urgent", "emergency"]

# =============================================================================
# PRIORITY CONFIGURATION (Timefold Enterprise Pattern)
# =============================================================================
priorityWeights:
  - priority: "EMERGENCY"
    weight: 1000                              # Rare emergencies in personal care
  - priority: "URGENT"
    weight: 100                               # Urgent personal care needs
  - priority: "PREFERRED"
    weight: 50                                # Preferred timing for comfort
  - priority: "HIGH"
    weight: 20                                # High priority routine care
  - priority: "NORMAL"
    weight: 10                                # Standard personal care visits
  - priority: "LOW"
    weight: 5                                 # Flexible routine visits
  - priority: "ROUTINE"
    weight: 1                                 # Basic maintenance visits

# =============================================================================
# TRAVEL TIME ADJUSTMENT (Timefold Enterprise Pattern)
# =============================================================================
travelTimeAdjustment:
  multiplier: 1.1                             # 10% buffer for parking and client interaction
  extraTime: "PT5M"                           # 5 minutes for personal connection

# =============================================================================
# VISIT COMPLETION RISK MANAGEMENT
# =============================================================================
visitCompletionRisk:
  minimalTimeToShiftEnd: "PT3H"               # 3 hours before shift end
  minimalPriority: "NORMAL"                   # Normal+ priority visits considered

score_type: "hard_soft"
environment_mode: "REPRODUCIBLE"

# Solver configuration - balanced optimization for relationship care
solver_config:
  time_limit_seconds: 90
  unimproved_seconds_spent_limit: 45
  best_score_limit: null
  
# Metadata for operational use
metadata:
  created_by: "system"
  created_date: "2024-01-01"
  last_modified: "2024-01-01"
  use_cases: ["personal_care", "companion_care", "adl_assistance", "homemaker_services"]
  target_services: ["bathing assistance", "meal preparation", "medication reminders", "transportation", "companionship"]
  regulatory_requirements: ["state_pcs_regulations", "medicaid_waiver", "background_checks"]
  complexity_level: "relationship_focused"
  description: "Optimized for personal care services emphasizing relationship-based care, patient comfort, and caregiver satisfaction. Prioritizes continuity, cultural matching, and preferences over clinical efficiency."
