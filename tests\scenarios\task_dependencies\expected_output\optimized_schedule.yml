# =============================================================================
# EXPECTED OUTPUT - Task Dependencies Scenario
# =============================================================================
# Optimal assignment for sequential wound care tasks with provider dependencies
# This represents the expected behavior when all constraints are satisfied

scenario: "Task Dependencies - Sequential Wound Care"
description: "Optimal assignment for sequential wound care tasks with provider dependencies"
optimization_date: "2024-01-15"
total_tasks: 4
total_providers: 4

# =============================================================================
# CONSTRAINT VALIDATION
# =============================================================================
constraint_validation:
  tasks_1_2_3_same_provider:
    required: true
    satisfied: true
    description: "Tasks 1, 2, 3 must be assigned to the same RN"
    
  tasks_1_2_3_sequential_order:
    required: true
    satisfied: true
    description: "Tasks 1, 2, 3 must execute in sequence order with proper timing"
    
  task_4_after_all_prerequisites:
    required: true
    satisfied: true
    description: "Task 4 can only start after tasks 1, 2, 3 are complete"
    
  task_4_any_provider_allowed:
    required: true
    satisfied: true
    description: "Task 4 can be assigned to any provider with documentation skills"
    
  buffer_time_between_tasks:
    required: true
    satisfied: true
    description: "Minimum 15-minute buffer between sequential tasks"

# =============================================================================
# OPTIMAL ASSIGNMENT
# =============================================================================
optimal_assignment:
  task_1_wound_assessment:
    task_id: "770e8400-e29b-41d4-a716-446655440001"
    task_type: "wound_assessment"
    assigned_provider:
      id: "550e8400-e29b-41d4-a716-446655440001"
      name: "Alice Johnson, RN"
      role: "Registered Nurse"
    scheduled_time:
      start: "2024-01-15T09:00:00"
      end: "2024-01-15T09:30:00"
      duration_minutes: 30
    location: "123 Main St, Queens, NY 11375"
    rationale: "First task in sequence, requires RN with wound care skills"

  task_2_wound_cleaning:
    task_id: "770e8400-e29b-41d4-a716-446655440002"
    task_type: "wound_cleaning"
    assigned_provider:
      id: "550e8400-e29b-41d4-a716-446655440001"
      name: "Alice Johnson, RN"
      role: "Registered Nurse"
    scheduled_time:
      start: "2024-01-15T09:45:00"
      end: "2024-01-15T10:30:00"
      duration_minutes: 45
    location: "123 Main St, Queens, NY 11375"
    rationale: "Second task in sequence, same RN required, 15-minute buffer after task 1"

  task_3_wound_dressing:
    task_id: "770e8400-e29b-41d4-a716-446655440003"
    task_type: "wound_dressing"
    assigned_provider:
      id: "550e8400-e29b-41d4-a716-446655440001"
      name: "Alice Johnson, RN"
      role: "Registered Nurse"
    scheduled_time:
      start: "2024-01-15T10:45:00"
      end: "2024-01-15T11:15:00"
      duration_minutes: 30
    location: "123 Main St, Queens, NY 11375"
    rationale: "Third task in sequence, same RN required, 15-minute buffer after task 2"

  task_4_documentation:
    task_id: "770e8400-e29b-41d4-a716-446655440004"
    task_type: "documentation"
    assigned_provider:
      id: "550e8400-e29b-41d4-a716-446655440003"
      name: "Carol Davis, CNA"
      role: "Certified Nursing Assistant"
    scheduled_time:
      start: "2024-01-15T11:30:00"
      end: "2024-01-15T11:45:00"
      duration_minutes: 15
    location: "123 Main St, Queens, NY 11375"
    rationale: "Documentation task, any provider allowed, 15-minute buffer after task 3 completion"

# =============================================================================
# SCHEDULE SUMMARY
# =============================================================================
schedule_summary:
  total_duration: "2 hours 45 minutes"
  start_time: "09:00:00"
  end_time: "11:45:00"
  providers_utilized: 2
  travel_time_total: "45 minutes"
  buffer_time_total: "45 minutes"

# =============================================================================
# PROVIDER SCHEDULES
# =============================================================================
provider_schedules:
  alice_johnson_rn:
    provider_id: "550e8400-e29b-41d4-a716-446655440001"
    total_tasks: 3
    total_time: "1 hour 45 minutes"
    schedule:
      - time: "09:00-09:30"
        task: "Wound Assessment"
        location: "Queens"
      - time: "09:30-09:45"
        activity: "Buffer/Travel"
        location: "Queens"
      - time: "09:45-10:30"
        task: "Wound Cleaning"
        location: "Queens"
      - time: "10:30-10:45"
        activity: "Buffer/Travel"
        location: "Queens"
      - time: "10:45-11:15"
        task: "Wound Dressing"
        location: "Queens"

  carol_davis_cna:
    provider_id: "550e8400-e29b-41d4-a716-446655440003"
    total_tasks: 1
    total_time: "15 minutes"
    schedule:
      - time: "11:30-11:45"
        task: "Documentation"
        location: "Queens"

# =============================================================================
# OPTIMIZATION METRICS
# =============================================================================
optimization_metrics:
  constraint_violations: 0
  hard_constraint_score: 0
  soft_constraint_score: -150
  total_travel_distance_miles: 15.2
  total_travel_time_minutes: 45
  provider_utilization:
    alice_johnson_rn: "21.9%"
    carol_davis_cna: "3.1%"
  patient_satisfaction_factors:
    continuity_of_care: "excellent"
    minimal_disruption: "excellent"
    preferred_time_windows: "good"

# =============================================================================
# WHY THIS IS OPTIMAL
# =============================================================================
why_this_is_optimal:
  dependency_compliance: "All task dependencies are satisfied with proper sequencing"
  provider_efficiency: "Alice handles all RN tasks in one continuous visit, minimizing travel"
  resource_optimization: "Carol handles documentation separately, allowing Alice to move to next patient"
  patient_experience: "Minimal disruption with one primary RN for all clinical tasks"
  cost_effectiveness: "Optimal use of higher-skilled RN time, delegation of documentation to CNA"
  quality_assurance: "Same RN maintains continuity through entire wound care procedure"
